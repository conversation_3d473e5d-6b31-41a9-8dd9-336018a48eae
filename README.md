# Complete Modern React Native Stack

A production-ready React Native mobile application built with the latest technologies and best practices.

## 🚀 Tech Stack

### Core Framework
- **React Native** with **Expo SDK 50+**
- **TypeScript** for type safety
- **Expo Router** for file-based routing

### UI & Styling
- **NativeWind** (Tailwind CSS for React Native)
- **Gluestack UI** for component library
- **Lucide React Native** for icons
- **React Native Reanimated 3** for animations

### State Management
- **Zustand** with localStorage persistence
- **TanStack Query** for server state management

### Forms & Validation
- **React Hook Form** for form handling
- **Zod** for schema validation
- **@hookform/resolvers** for integration

### HTTP & API
- **Axios** for HTTP requests
- **TanStack Query** for caching and synchronization

### Authentication & Storage
- **Expo Auth Session** for OAuth
- **Expo SecureStore** for secure token storage
- **localStorage** for cross-platform key-value storage

### Device Features
- **Expo Device** for device information (removed for now)

## 📱 Features

### Authentication
- Email/password authentication
- OAuth integration (Google, Apple, Facebook)
- Secure token storage
- Automatic token refresh
- Biometric authentication support

### Navigation
- File-based routing with Expo Router
- Tab navigation
- Stack navigation
- Deep linking support

### UI Components
- Reusable component library
- Consistent design system
- Dark/light theme support
- Responsive design

### Forms & Validation
- Type-safe form handling
- Real-time validation
- Custom form components
- Error handling

### Data Management
- Optimistic updates
- Background sync
- Offline support
- Caching strategies

### Push Notifications
- Local notifications
- Remote push notifications
- Notification categories
- Badge management

## 🏗️ Project Structure

```
├── app/                    # Expo Router pages
│   ├── (tabs)/            # Tab navigation group
│   │   ├── index.tsx      # Home screen
│   │   ├── notifications.tsx
│   │   ├── profile.tsx
│   │   └── settings.tsx
│   ├── _layout.tsx        # Root layout
│   ├── auth.tsx          # Authentication screen
│   └── index.tsx         # Entry point
├── src/
│   ├── components/        # Reusable components
│   │   ├── ui/           # Base UI components
│   │   └── forms/        # Form components
│   ├── hooks/            # Custom hooks
│   ├── schemas/          # Zod validation schemas
│   ├── services/         # API and external services
│   ├── stores/           # Zustand stores
│   └── utils/            # Utility functions
├── assets/               # Static assets
└── ...config files
```

## 🛠️ Setup & Installation

### Prerequisites
- Node.js 18+
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mobile_app
   ```

2. **Install dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the development server**
   ```bash
   npx expo start
   ```

5. **Run on device/simulator**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app

## 🔧 Configuration

### Environment Variables
Create a `.env` file based on `.env.example`:

```env
EXPO_PUBLIC_API_URL=https://your-api.com
EXPO_PUBLIC_PROJECT_ID=your-expo-project-id
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

### Push Notifications
1. Configure your Expo project ID in `app.json`
2. Set up push notification credentials
3. Configure notification channels (Android)

### OAuth Setup
1. Configure OAuth providers in your backend
2. Add client IDs to environment variables
3. Set up URL schemes for deep linking

## 📚 Usage Examples

### Authentication
```typescript
import { useAuthStore } from '../src/stores/authStore';

const { signIn, signUp, signOut, user, isLoading } = useAuthStore();

// Sign in
await signIn('<EMAIL>', 'password');

// Sign up
await signUp('<EMAIL>', 'password', 'Full Name');
```

### API Calls
```typescript
import { useUsers, useCreatePost } from '../src/hooks/useApi';

// Fetch data
const { data: users, isLoading } = useUsers();

// Mutations
const createPost = useCreatePost({
  onSuccess: () => {
    console.log('Post created successfully');
  },
});
```

### Forms
```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormInput } from '../src/components/forms/FormInput';

const form = useForm({
  resolver: zodResolver(schema),
});

<FormInput
  name="email"
  control={form.control}
  label="Email"
  placeholder="Enter email"
/>
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## 📦 Building

### Development Build
```bash
npx expo build:android --type apk
npx expo build:ios --type simulator
```

### Production Build
```bash
# Android
eas build --platform android --profile production

# iOS
eas build --platform ios --profile production
```

## 🚀 Deployment

### Expo Application Services (EAS)
```bash
# Configure EAS
eas build:configure

# Build for stores
eas build --platform all --profile production

# Submit to stores
eas submit --platform all
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/example)
- 📖 Documentation: [docs.example.com](https://docs.example.com)

## 🙏 Acknowledgments

- Expo team for the amazing development platform
- React Native community for continuous improvements
- All contributors and maintainers
