{"buildFiles": ["C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\.cxx\\Debug\\4a64595b\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\.cxx\\Debug\\4a64595b\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\build\\intermediates\\cxx\\Debug\\4a64595b\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_reactnativemmkv::@53a90ad335aa3dba27a5": {"artifactName": "react_codegen_reactnativemmkv", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\build\\intermediates\\cxx\\Debug\\4a64595b\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\build\\intermediates\\cxx\\Debug\\4a64595b\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\build\\intermediates\\cxx\\Debug\\4a64595b\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\build\\intermediates\\cxx\\Debug\\4a64595b\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\build\\intermediates\\cxx\\Debug\\4a64595b\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"artifactName": "react_codegen_RNEdgeToEdge", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\Documents\\augment-projects\\mobile_app\\android\\app\\build\\intermediates\\cxx\\Debug\\4a64595b\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "armeabi-v7a", "runtimeFiles": []}}}