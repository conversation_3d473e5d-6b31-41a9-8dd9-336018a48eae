ninja: Entering directory `C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\.cxx\Debug\4a64595b\x86_64'
[0/2] Re-checking globbed directories...
[1/69] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/69] Building CXX object reactnativemmkv_autolinked_build/CMakeFiles/react_codegen_reactnativemmkv.dir/react/renderer/components/reactnativemmkv/States.cpp.o
[3/69] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[4/69] Building CXX object reactnativemmkv_autolinked_build/CMakeFiles/react_codegen_reactnativemmkv.dir/react/renderer/components/reactnativemmkv/EventEmitters.cpp.o
[5/69] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[6/69] Building CXX object reactnativemmkv_autolinked_build/CMakeFiles/react_codegen_reactnativemmkv.dir/react/renderer/components/reactnativemmkv/reactnativemmkvJSI-generated.cpp.o
[7/69] Building CXX object reactnativemmkv_autolinked_build/CMakeFiles/react_codegen_reactnativemmkv.dir/react/renderer/components/reactnativemmkv/Props.cpp.o
[8/69] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[9/69] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[10/69] Building CXX object reactnativemmkv_autolinked_build/CMakeFiles/react_codegen_reactnativemmkv.dir/reactnativemmkv-generated.cpp.o
[11/69] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[12/69] Building CXX object reactnativemmkv_autolinked_build/CMakeFiles/react_codegen_reactnativemmkv.dir/react/renderer/components/reactnativemmkv/ShadowNodes.cpp.o
[13/69] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[14/69] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[15/69] Building CXX object reactnativemmkv_autolinked_build/CMakeFiles/react_codegen_reactnativemmkv.dir/react/renderer/components/reactnativemmkv/ComponentDescriptors.cpp.o
[16/69] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[17/69] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[18/69] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[19/69] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[20/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1e99cc36ff3f415e69ef97768ab452f8/components/safeareacontext/States.cpp.o
[21/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9b3c89289cebbfeebe02aeb408b35878/safeareacontext/EventEmitters.cpp.o
[22/69] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[23/69] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[24/69] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[25/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/16552ff60a77b168d9844c0fe94924db/safeareacontextJSI-generated.cpp.o
[26/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7d43b17b9660078f766e932dce50babe/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[27/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/0782c1123df5107d2f2553d47427dc21/safeareacontext/RNCSafeAreaViewState.cpp.o
[28/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1e99cc36ff3f415e69ef97768ab452f8/components/safeareacontext/ShadowNodes.cpp.o
[29/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f98794bebe9c96e12b8eb04a0dd12450/codegen/jni/safeareacontext-generated.cpp.o
[30/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/1e99cc36ff3f415e69ef97768ab452f8/components/safeareacontext/Props.cpp.o
[31/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7d43b17b9660078f766e932dce50babe/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[32/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e6f45efe4a4cc45987835af03e95f2b0/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[33/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6dde4cd9a2966561566299d8f630ca2b/RNCSafeAreaViewShadowNode.cpp.o
[34/69] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/9b3c89289cebbfeebe02aeb408b35878/safeareacontext/ComponentDescriptors.cpp.o
[35/69] Linking CXX shared library C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\intermediates\cxx\Debug\4a64595b\obj\x86_64\libreact_codegen_safeareacontext.so
[36/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7d43b17b9660078f766e932dce50babe/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[37/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0d610fa633827aa4a0221b57463f2e4e/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[38/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8d6765cc910f0c0194d45215788a07b9/components/rnscreens/rnscreensJSI-generated.cpp.o
[39/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/963f7dc0f841053a8a1f849e9569ee3f/jni/react/renderer/components/rnscreens/States.cpp.o
[40/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e6f45efe4a4cc45987835af03e95f2b0/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[41/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3c0c03ebef8215811bb1e8a9b5982ce0/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[42/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7d43b17b9660078f766e932dce50babe/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[43/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2cb10cf87edabfd0ede90308c29e201c/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o
[44/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[45/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/530aaf8fe7bfb09e26207cef78c0e75b/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[46/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/963f7dc0f841053a8a1f849e9569ee3f/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[47/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/963f7dc0f841053a8a1f849e9569ee3f/jni/react/renderer/components/rnscreens/Props.cpp.o
[48/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/56dc10caedf94a0cdd00ff0f46c67c91/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   31 | void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
      |                                            ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                          ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                           ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:33:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   33 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |   source.setProperty(runtime, "width", $event.source.width);
      |                                        ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   37 |   source.setProperty(runtime, "height", $event.source.height);
      |                                         ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |   source.setProperty(runtime, "uri", $event.source.uri);
      |                                      ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:39:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   39 |   $payload.setProperty(runtime, "source", source);
      |   ^
C:/Users/<USER>/Documents/augment-projects/mobile_app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:41:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   41 |     return $payload;
      |            ^
9 warnings generated.
[49/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/58e8cbf55c0e5ab68bb6d05c3ba2b5a0/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o
[50/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/53595226db775ee3cfd5c6b66c98b38f/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[51/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/58e8cbf55c0e5ab68bb6d05c3ba2b5a0/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[52/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/2269241165d59683cc122a8febb3e350/react/renderer/components/rnscreens/EventEmitters.cpp.o
[53/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/91a6ce47e0c0fbab649ff1532c21081f/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[54/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/91a6ce47e0c0fbab649ff1532c21081f/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[55/69] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/381c3b4f89311bbe64281c266e885e94/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[56/69] Linking CXX shared library C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\intermediates\cxx\Debug\4a64595b\obj\x86_64\libreact_codegen_rnscreens.so
[57/69] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Documents/augment-projects/mobile_app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[58/69] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o
[59/69] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o
[60/69] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o
[61/69] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o
[62/69] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o
[63/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/56dc10caedf94a0cdd00ff0f46c67c91/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[64/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[65/69] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o
[66/69] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o
[67/69] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/53595226db775ee3cfd5c6b66c98b38f/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[68/69] Linking CXX shared library C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\intermediates\cxx\Debug\4a64595b\obj\x86_64\libreact_codegen_rnsvg.so
[69/69] Linking CXX shared library C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\intermediates\cxx\Debug\4a64595b\obj\x86_64\libappmodules.so
