<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.EdgeToEdge" parent="Theme.EdgeToEdge.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
    <style name="Theme.EdgeToEdge.Light" parent="Theme.EdgeToEdge.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
    <style name="Theme.EdgeToEdge.Material2" parent="Theme.EdgeToEdge.Material2.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
    <style name="Theme.EdgeToEdge.Material2.Light" parent="Theme.EdgeToEdge.Material2.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
    <style name="Theme.EdgeToEdge.Material3" parent="Theme.EdgeToEdge.Material3.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
    <style name="Theme.EdgeToEdge.Material3.Dynamic" parent="Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common">
        <item name="android:windowLightNavigationBar">@bool/windowLightSystemBars</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
    <style name="Theme.EdgeToEdge.Material3.Dynamic.Light" parent="Theme.EdgeToEdge.Material3.Dynamic.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
    <style name="Theme.EdgeToEdge.Material3.Light" parent="Theme.EdgeToEdge.Material3.Light.Common">
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">always</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">?enforceNavigationBarContrast</item>
    </style>
</resources>