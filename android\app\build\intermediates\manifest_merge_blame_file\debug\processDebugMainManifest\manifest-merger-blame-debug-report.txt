1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gss.goodkey"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:3:3-77
13-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:3:20-75
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:5:3-63
14-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:5:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:6:3-78
15-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:6:20-76
16
17    <queries>
17-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:7:3-13:13
18        <intent>
18-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:8:5-12:14
19            <action android:name="android.intent.action.VIEW" />
19-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:7-58
19-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:15-56
20
21            <category android:name="android.intent.category.BROWSABLE" />
21-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:7-67
21-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:17-65
22
23            <data android:scheme="https" />
23-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
23-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
24        </intent>
25
26        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
26-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
26-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
27        <intent>
27-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
28            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
28-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
28-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
29        </intent>
30        <intent>
30-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
31
32            <!-- Required for opening tabs if targeting API 30 -->
33            <action android:name="android.support.customtabs.action.CustomTabsService" />
33-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
33-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
34        </intent>
35    </queries>
36
37    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
37-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
37-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
38    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
38-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
38-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.gss.goodkey.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.gss.goodkey.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
45-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
45-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
46
47    <application
47-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:3-31:17
48        android:name="com.gss.goodkey.MainApplication"
48-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:16-47
49        android:allowBackup="true"
49-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:162-188
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
51        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
51-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:307-376
52        android:debuggable="true"
53        android:extractNativeLibs="false"
54        android:fullBackupContent="@xml/secure_store_backup_rules"
54-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:248-306
55        android:icon="@mipmap/ic_launcher"
55-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:81-115
56        android:label="@string/app_name"
56-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:48-80
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:116-161
58        android:supportsRtl="true"
58-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:221-247
59        android:theme="@style/AppTheme"
59-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:189-220
60        android:usesCleartextTraffic="true" >
60-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\debug\AndroidManifest.xml:6:18-53
61        <meta-data
61-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:15:5-83
62            android:name="expo.modules.updates.ENABLED"
62-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:15:16-59
63            android:value="false" />
63-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:15:60-81
64        <meta-data
64-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:16:5-105
65            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
65-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:16:16-80
66            android:value="ALWAYS" />
66-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:16:81-103
67        <meta-data
67-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:17:5-99
68            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
68-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:17:16-79
69            android:value="0" />
69-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:17:80-97
70
71        <activity
71-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:5-30:16
72            android:name="com.gss.goodkey.MainActivity"
72-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:15-43
73            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
73-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:44-134
74            android:exported="true"
74-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:256-279
75            android:launchMode="singleTask"
75-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:135-166
76            android:screenOrientation="portrait"
76-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:280-316
77            android:theme="@style/Theme.App.SplashScreen"
77-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:210-255
78            android:windowSoftInputMode="adjustResize" >
78-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:167-209
79            <intent-filter>
79-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:19:7-22:23
80                <action android:name="android.intent.action.MAIN" />
80-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:20:9-60
80-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:20:17-58
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:21:9-68
82-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:21:19-66
83            </intent-filter>
84            <intent-filter>
84-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:23:7-29:23
85                <action android:name="android.intent.action.VIEW" />
85-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:7-58
85-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:15-56
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:9-67
87-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:19-65
88                <category android:name="android.intent.category.BROWSABLE" />
88-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:7-67
88-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:17-65
89
90                <data android:scheme="mobile-app" />
90-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
90-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
91                <data android:scheme="exp+mobileapp" />
91-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
91-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
92            </intent-filter>
93        </activity>
94        <activity
94-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
95            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
95-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
96            android:exported="true"
96-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
97            android:launchMode="singleTask"
97-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
98            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
98-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
99            <intent-filter>
99-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
100                <action android:name="android.intent.action.VIEW" />
100-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:7-58
100-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:15-56
101
102                <category android:name="android.intent.category.DEFAULT" />
102-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:9-67
102-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:19-65
103                <category android:name="android.intent.category.BROWSABLE" />
103-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:7-67
103-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:17-65
104
105                <data android:scheme="expo-dev-launcher" />
105-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
105-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
106            </intent-filter>
107        </activity>
108        <activity
108-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
109            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
109-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
110            android:screenOrientation="portrait"
110-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
111            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
111-->[:expo-dev-launcher] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
112        <activity
112-->[:expo-dev-menu] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
113            android:name="expo.modules.devmenu.DevMenuActivity"
113-->[:expo-dev-menu] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
114            android:exported="true"
114-->[:expo-dev-menu] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
115            android:launchMode="singleTask"
115-->[:expo-dev-menu] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
116            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
116-->[:expo-dev-menu] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
117            <intent-filter>
117-->[:expo-dev-menu] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
118                <action android:name="android.intent.action.VIEW" />
118-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:7-58
118-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:15-56
119
120                <category android:name="android.intent.category.DEFAULT" />
120-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:9-67
120-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:19-65
121                <category android:name="android.intent.category.BROWSABLE" />
121-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:7-67
121-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:17-65
122
123                <data android:scheme="expo-dev-menu" />
123-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
123-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
124            </intent-filter>
125        </activity>
126
127        <meta-data
127-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
128            android:name="org.unimodules.core.AppLoader#react-native-headless"
128-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
129            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
129-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
130        <meta-data
130-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
131            android:name="com.facebook.soloader.enabled"
131-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
132            android:value="true" />
132-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
133
134        <activity
134-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
135            android:name="com.facebook.react.devsupport.DevSettingsActivity"
135-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
136            android:exported="false" />
136-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
137
138        <provider
138-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
139            android:name="expo.modules.filesystem.FileSystemFileProvider"
139-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
140            android:authorities="com.gss.goodkey.FileSystemFileProvider"
140-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
141            android:exported="false"
141-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
142            android:grantUriPermissions="true" >
142-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
143            <meta-data
143-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
144                android:name="android.support.FILE_PROVIDER_PATHS"
144-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
145                android:resource="@xml/file_system_provider_paths" />
145-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
146        </provider>
147        <provider
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
148            android:name="androidx.startup.InitializationProvider"
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
149            android:authorities="com.gss.goodkey.androidx-startup"
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
150            android:exported="false" >
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
151            <meta-data
151-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
152                android:name="androidx.emoji2.text.EmojiCompatInitializer"
152-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
153                android:value="androidx.startup" />
153-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
154            <meta-data
154-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
155                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
155-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
156                android:value="androidx.startup" />
156-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
157            <meta-data
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
158                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
159                android:value="androidx.startup" />
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
160        </provider>
161
162        <receiver
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
163            android:name="androidx.profileinstaller.ProfileInstallReceiver"
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
164            android:directBootAware="false"
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
165            android:enabled="true"
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
166            android:exported="true"
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
167            android:permission="android.permission.DUMP" >
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
168            <intent-filter>
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
169                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
170            </intent-filter>
171            <intent-filter>
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
172                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
173            </intent-filter>
174            <intent-filter>
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
175                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
176            </intent-filter>
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
178                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
179            </intent-filter>
180        </receiver>
181    </application>
182
183</manifest>
