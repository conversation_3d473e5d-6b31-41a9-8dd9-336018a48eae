1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gss.goodkey"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:2:3-64
11-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:3:3-77
12-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:3:20-75
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:4:3-75
13-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:4:20-73
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:5:3-63
14-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:5:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:6:3-78
15-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:6:20-76
16
17    <queries>
17-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:7:3-13:13
18        <intent>
18-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:8:5-12:14
19            <action android:name="android.intent.action.VIEW" />
19-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:7-58
19-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:15-56
20
21            <category android:name="android.intent.category.BROWSABLE" />
21-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:7-67
21-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:17-65
22
23            <data android:scheme="https" />
23-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
23-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
24        </intent>
25        <!-- Query open documents -->
26        <intent>
26-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
27            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
27-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
27-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
28        </intent>
29        <intent>
29-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
30
31            <!-- Required for opening tabs if targeting API 30 -->
32            <action android:name="android.support.customtabs.action.CustomTabsService" />
32-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
32-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b88692d87c8bd81b3c7040f478064fc4\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
36-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
36-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
37    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
37-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
37-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
38
39    <permission
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
40        android:name="com.gss.goodkey.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.gss.goodkey.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
44    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
44-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
44-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c606388d75cd1d2425bd93631a9c5065\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
45
46    <application
46-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:3-31:17
47        android:name="com.gss.goodkey.MainApplication"
47-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:16-47
48        android:allowBackup="true"
48-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:162-188
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
50-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:307-376
51        android:extractNativeLibs="false"
52        android:fullBackupContent="@xml/secure_store_backup_rules"
52-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:248-306
53        android:icon="@mipmap/ic_launcher"
53-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:81-115
54        android:label="@string/app_name"
54-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:48-80
55        android:roundIcon="@mipmap/ic_launcher_round"
55-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:116-161
56        android:supportsRtl="true"
56-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:221-247
57        android:theme="@style/AppTheme" >
57-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:14:189-220
58        <meta-data
58-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:15:5-83
59            android:name="expo.modules.updates.ENABLED"
59-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:15:16-59
60            android:value="false" />
60-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:15:60-81
61        <meta-data
61-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:16:5-105
62            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
62-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:16:16-80
63            android:value="ALWAYS" />
63-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:16:81-103
64        <meta-data
64-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:17:5-99
65            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
65-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:17:16-79
66            android:value="0" />
66-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:17:80-97
67
68        <activity
68-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:5-30:16
69            android:name="com.gss.goodkey.MainActivity"
69-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:15-43
70            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
70-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:44-134
71            android:exported="true"
71-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:256-279
72            android:launchMode="singleTask"
72-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:135-166
73            android:screenOrientation="portrait"
73-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:280-316
74            android:theme="@style/Theme.App.SplashScreen"
74-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:210-255
75            android:windowSoftInputMode="adjustResize" >
75-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:18:167-209
76            <intent-filter>
76-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:19:7-22:23
77                <action android:name="android.intent.action.MAIN" />
77-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:20:9-60
77-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:20:17-58
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:21:9-68
79-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:21:19-66
80            </intent-filter>
81            <intent-filter>
81-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:23:7-29:23
82                <action android:name="android.intent.action.VIEW" />
82-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:7-58
82-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:9:15-56
83
84                <category android:name="android.intent.category.DEFAULT" />
84-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:9-67
84-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:25:19-65
85                <category android:name="android.intent.category.BROWSABLE" />
85-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:7-67
85-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:10:17-65
86
87                <data android:scheme="mobile-app" />
87-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
87-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
88                <data android:scheme="exp+goodkey" />
88-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:7-37
88-->C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\AndroidManifest.xml:11:13-35
89            </intent-filter>
90        </activity>
91
92        <meta-data
92-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
93            android:name="org.unimodules.core.AppLoader#react-native-headless"
93-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
94            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
94-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
95        <meta-data
95-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
96            android:name="com.facebook.soloader.enabled"
96-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
97            android:value="true" />
97-->[:expo-modules-core] C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
98
99        <provider
99-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
100            android:name="expo.modules.filesystem.FileSystemFileProvider"
100-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
101            android:authorities="com.gss.goodkey.FileSystemFileProvider"
101-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
102            android:exported="false"
102-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
103            android:grantUriPermissions="true" >
103-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
104            <meta-data
104-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
105                android:name="android.support.FILE_PROVIDER_PATHS"
105-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
106                android:resource="@xml/file_system_provider_paths" />
106-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
107        </provider>
108        <provider
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
109            android:name="androidx.startup.InitializationProvider"
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
110            android:authorities="com.gss.goodkey.androidx-startup"
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
111            android:exported="false" >
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
112            <meta-data
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
113                android:name="androidx.emoji2.text.EmojiCompatInitializer"
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
114                android:value="androidx.startup" />
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
115            <meta-data
115-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
116-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
117                android:value="androidx.startup" />
117-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
118            <meta-data
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
120                android:value="androidx.startup" />
120-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
121        </provider>
122
123        <receiver
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
124            android:name="androidx.profileinstaller.ProfileInstallReceiver"
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
125            android:directBootAware="false"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
126            android:enabled="true"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
127            android:exported="true"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
128            android:permission="android.permission.DUMP" >
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
129            <intent-filter>
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
130                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
131            </intent-filter>
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
133                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
136                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
139                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
140            </intent-filter>
141        </receiver>
142    </application>
143
144</manifest>
