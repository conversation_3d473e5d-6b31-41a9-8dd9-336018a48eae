{"logs": [{"outputFile": "com.gss.goodkey.app-mergeDebugResources-47:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "39,40,41,42,43,44,45,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3567,3670,3768,3868,3969,4081,13439", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3562,3665,3763,3863,3964,4076,4188,13535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,353,437,503,572,648,725,808,888,959,1036,1118,1195,1278,1360,1436,1507,1577,1670,1749,1823,1902", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "123,206,278,348,432,498,567,643,720,803,883,954,1031,1113,1190,1273,1355,1431,1502,1572,1665,1744,1818,1897,1974"}, "to": {"startLines": "33,49,69,71,72,74,88,89,90,137,138,139,140,145,146,147,148,149,150,151,152,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,4485,6776,6918,6988,7134,8153,8222,8298,12174,12257,12337,12408,12805,12887,12964,13047,13129,13205,13276,13346,13540,13619,13693,13772", "endColumns": "72,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "3075,4563,6843,6983,7067,7195,8217,8293,8370,12252,12332,12403,12480,12882,12959,13042,13124,13200,13271,13341,13434,13614,13688,13767,13844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1019,1084,1178,1248,1310,1397,1460,1525,1584,1649,1710,1767,1886,1944,2005,2062,2133,2263,2349,2425,2510,2592,2670,2808,2883,2954,3104,3201,3279,3334,3390,3456,3536,3626,3697,3782,3861,3938,4008,4083,4195,4283,4356,4456,4555,4629,4705,4812,4866,4956,5029,5120,5216,5278,5342,5405,5476,5575,5673,5765,5861,5919,5979,6062,6144,6222", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "264,342,418,498,577,656,756,868,948,1014,1079,1173,1243,1305,1392,1455,1520,1579,1644,1705,1762,1881,1939,2000,2057,2128,2258,2344,2420,2505,2587,2665,2803,2878,2949,3099,3196,3274,3329,3385,3451,3531,3621,3692,3777,3856,3933,4003,4078,4190,4278,4351,4451,4550,4624,4700,4807,4861,4951,5024,5115,5211,5273,5337,5400,5471,5570,5668,5760,5856,5914,5974,6057,6139,6217,6293"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3158,3234,3314,3393,4193,4293,4405,4876,4942,5007,6848,7072,7200,7287,7350,7415,7474,7539,7600,7657,7776,7834,7895,7952,8023,8375,8461,8537,8622,8704,8782,8920,8995,9066,9216,9313,9391,9446,9502,9568,9648,9738,9809,9894,9973,10050,10120,10195,10307,10395,10468,10568,10667,10741,10817,10924,10978,11068,11141,11232,11328,11390,11454,11517,11588,11687,11785,11877,11973,12031,12091,12569,12651,12729", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,142,143,144", "endColumns": "12,77,75,79,78,78,99,111,79,65,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,75,84,81,77,137,74,70,149,96,77,54,55,65,79,89,70,84,78,76,69,74,111,87,72,99,98,73,75,106,53,89,72,90,95,61,63,62,70,98,97,91,95,57,59,82,81,77,75", "endOffsets": "314,3153,3229,3309,3388,3467,4288,4400,4480,4937,5002,5096,6913,7129,7282,7345,7410,7469,7534,7595,7652,7771,7829,7890,7947,8018,8148,8456,8532,8617,8699,8777,8915,8990,9061,9211,9308,9386,9441,9497,9563,9643,9733,9804,9889,9968,10045,10115,10190,10302,10390,10463,10563,10662,10736,10812,10919,10973,11063,11136,11227,11323,11385,11449,11512,11583,11682,11780,11872,11968,12026,12086,12169,12646,12724,12800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,12485", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,12564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4678,5101,5198,5331", "endColumns": "96,96,132,99", "endOffsets": "4770,5193,5326,5426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,266,385,517,651,810,940,1098,1202,1342,1481", "endColumns": "109,100,118,131,133,158,129,157,103,139,138,129", "endOffsets": "160,261,380,512,646,805,935,1093,1197,1337,1476,1606"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4568,4775,5431,5550,5682,5816,5975,6105,6263,6367,6507,6646", "endColumns": "109,100,118,131,133,158,129,157,103,139,138,129", "endOffsets": "4673,4871,5545,5677,5811,5970,6100,6258,6362,6502,6641,6771"}}]}]}