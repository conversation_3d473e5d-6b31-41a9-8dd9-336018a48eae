{"logs": [{"outputFile": "com.gss.goodkey.app-mergeDebugResources-47:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1078,1172,1246,1305,1391,1453,1514,1572,1636,1697,1751,1868,1925,1985,2039,2114,2241,2325,2405,2500,2584,2662,2792,2876,2954,3088,3179,3260,3311,3362,3428,3496,3572,3643,3723,3802,3877,3950,4026,4132,4221,4298,4389,4483,4557,4627,4720,4769,4850,4916,5001,5087,5149,5213,5276,5347,5446,5551,5649,5754,5809,5864,5942,6024,6103", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1073,1167,1241,1300,1386,1448,1509,1567,1631,1692,1746,1863,1920,1980,2034,2109,2236,2320,2400,2495,2579,2657,2787,2871,2949,3083,3174,3255,3306,3357,3423,3491,3567,3638,3718,3797,3872,3945,4021,4127,4216,4293,4384,4478,4552,4622,4715,4764,4845,4911,4996,5082,5144,5208,5271,5342,5441,5546,5644,5749,5804,5859,5937,6019,6098,6172"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3160,3237,3315,3395,4205,4304,4418,4894,4957,5020,6790,7019,7149,7235,7297,7358,7416,7480,7541,7595,7712,7769,7829,7883,7958,8242,8326,8406,8501,8585,8663,8793,8877,8955,9089,9180,9261,9312,9363,9429,9497,9573,9644,9724,9803,9878,9951,10027,10133,10222,10299,10390,10484,10558,10628,10721,10770,10851,10917,11002,11088,11150,11214,11277,11348,11447,11552,11650,11755,11810,11865,12272,12354,12433", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,140,141,142", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "310,3155,3232,3310,3390,3470,4299,4413,4493,4952,5015,5109,6859,7073,7230,7292,7353,7411,7475,7536,7590,7707,7764,7824,7878,7953,8080,8321,8401,8496,8580,8658,8788,8872,8950,9084,9175,9256,9307,9358,9424,9492,9568,9639,9719,9798,9873,9946,10022,10128,10217,10294,10385,10479,10553,10623,10716,10765,10846,10912,10997,11083,11145,11209,11272,11343,11442,11547,11645,11750,11805,11860,11938,12349,12428,12502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,257,373,501,631,763,877,1012,1118,1256,1402", "endColumns": "106,94,115,127,129,131,113,134,105,137,145,122", "endOffsets": "157,252,368,496,626,758,872,1007,1113,1251,1397,1520"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4575,4799,5450,5566,5694,5824,5956,6070,6205,6311,6449,6595", "endColumns": "106,94,115,127,129,131,113,134,105,137,145,122", "endOffsets": "4677,4889,5561,5689,5819,5951,6065,6200,6306,6444,6590,6713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4682,5114,5225,5339", "endColumns": "116,110,113,110", "endOffsets": "4794,5220,5334,5445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,12187", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,12267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,349,432,503,579,660,743,826,904,981,1053,1137,1220,1297,1373,1448,1538,1611,1690,1764", "endColumns": "72,76,71,71,82,70,75,80,82,82,77,76,71,83,82,76,75,74,89,72,78,73,78", "endOffsets": "123,200,272,344,427,498,574,655,738,821,899,976,1048,1132,1215,1292,1368,1443,1533,1606,1685,1759,1838"}, "to": {"startLines": "33,49,69,71,72,74,88,89,136,137,138,143,144,145,146,147,148,149,150,152,153,154,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3008,4498,6718,6864,6936,7078,8085,8161,11943,12026,12109,12507,12584,12656,12740,12823,12900,12976,13051,13242,13315,13394,13468", "endColumns": "72,76,71,71,82,70,75,80,82,82,77,76,71,83,82,76,75,74,89,72,78,73,78", "endOffsets": "3076,4570,6785,6931,7014,7144,8156,8237,12021,12104,12182,12579,12651,12735,12818,12895,12971,13046,13136,13310,13389,13463,13542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "39,40,41,42,43,44,45,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3475,3572,3674,3773,3873,3976,4089,13141", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3567,3669,3768,3868,3971,4084,4200,13237"}}]}]}