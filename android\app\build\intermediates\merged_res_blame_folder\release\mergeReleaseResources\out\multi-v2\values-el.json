{"logs": [{"outputFile": "com.gss.goodkey.app-mergeReleaseResources-48:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0022c038c951d6e7d28f4306e8d52cd5\\transformed\\react-android-0.79.5-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,291,361,445,517,585,663,744,828,920,992,1074,1161,1245,1330,1413,1493,1564,1634,1722,1794,1874,1948", "endColumns": "73,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "124,210,286,356,440,512,580,658,739,823,915,987,1069,1156,1240,1325,1408,1488,1559,1629,1717,1789,1869,1943,2025"}, "to": {"startLines": "33,49,69,71,72,74,88,89,90,137,138,139,140,145,146,147,148,149,150,151,152,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3101,4657,7014,7172,7242,7389,8463,8531,8609,12545,12629,12721,12793,13205,13292,13376,13461,13544,13624,13695,13765,13954,14026,14106,14180", "endColumns": "73,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "3170,4738,7085,7237,7321,7456,8526,8604,8685,12624,12716,12788,12870,13287,13371,13456,13539,13619,13690,13760,13848,14021,14101,14175,14257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4860,5290,5397,5522", "endColumns": "109,106,124,109", "endOffsets": "4965,5392,5517,5627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1080,1146,1246,1328,1391,1482,1545,1610,1672,1741,1803,1857,1995,2052,2113,2167,2240,2393,2478,2557,2653,2737,2821,2960,3041,3126,3267,3357,3443,3498,3549,3615,3693,3778,3849,3932,4004,4084,4164,4235,4342,4434,4506,4603,4700,4774,4848,4950,5006,5093,5165,5253,5345,5407,5471,5534,5604,5720,5829,5938,6043,6102,6157,6248,6336,6411", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "265,353,439,524,620,707,809,926,1012,1075,1141,1241,1323,1386,1477,1540,1605,1667,1736,1798,1852,1990,2047,2108,2162,2235,2388,2473,2552,2648,2732,2816,2955,3036,3121,3262,3352,3438,3493,3544,3610,3688,3773,3844,3927,3999,4079,4159,4230,4337,4429,4501,4598,4695,4769,4843,4945,5001,5088,5160,5248,5340,5402,5466,5529,5599,5715,5824,5933,6038,6097,6152,6243,6331,6406,6487"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3175,3263,3349,3434,3530,4352,4454,4571,5061,5124,5190,7090,7326,7461,7552,7615,7680,7742,7811,7873,7927,8065,8122,8183,8237,8310,8690,8775,8854,8950,9034,9118,9257,9338,9423,9564,9654,9740,9795,9846,9912,9990,10075,10146,10229,10301,10381,10461,10532,10639,10731,10803,10900,10997,11071,11145,11247,11303,11390,11462,11550,11642,11704,11768,11831,11901,12017,12126,12235,12340,12399,12454,12961,13049,13124", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,142,143,144", "endColumns": "12,87,85,84,95,86,101,116,85,62,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,78,95,83,83,138,80,84,140,89,85,54,50,65,77,84,70,82,71,79,79,70,106,91,71,96,96,73,73,101,55,86,71,87,91,61,63,62,69,115,108,108,104,58,54,90,87,74,80", "endOffsets": "315,3258,3344,3429,3525,3612,4449,4566,4652,5119,5185,5285,7167,7384,7547,7610,7675,7737,7806,7868,7922,8060,8117,8178,8232,8305,8458,8770,8849,8945,9029,9113,9252,9333,9418,9559,9649,9735,9790,9841,9907,9985,10070,10141,10224,10296,10376,10456,10527,10634,10726,10798,10895,10992,11066,11140,11242,11298,11385,11457,11545,11637,11699,11763,11826,11896,12012,12121,12230,12335,12394,12449,12540,13044,13119,13200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "320,438,549,666,751,857,980,1069,1154,1245,1338,1433,1527,1627,1720,1815,1912,2003,2094,2179,2290,2399,2501,2612,2722,2830,3001,12875", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "433,544,661,746,852,975,1064,1149,1240,1333,1428,1522,1622,1715,1810,1907,1998,2089,2174,2285,2394,2496,2607,2717,2825,2996,3096,12956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,263,383,535,688,828,966,1119,1222,1365,1512", "endColumns": "116,90,119,151,152,139,137,152,102,142,146,132", "endOffsets": "167,258,378,530,683,823,961,1114,1217,1360,1507,1640"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4743,4970,5632,5752,5904,6057,6197,6335,6488,6591,6734,6881", "endColumns": "116,90,119,151,152,139,137,152,102,142,146,132", "endOffsets": "4855,5056,5747,5899,6052,6192,6330,6483,6586,6729,6876,7009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "39,40,41,42,43,44,45,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3617,3715,3818,3918,4021,4129,4235,13853", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3710,3813,3913,4016,4124,4230,4347,13949"}}]}]}