{"logs": [{"outputFile": "com.gss.goodkey.app-mergeReleaseResources-48:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0022c038c951d6e7d28f4306e8d52cd5\\transformed\\react-android-0.79.5-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,287,356,437,504,575,656,739,823,912,984,1070,1153,1229,1309,1391,1470,1548,1624,1714,1787,1866,1944", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,208,282,351,432,499,570,651,734,818,907,979,1065,1148,1224,1304,1386,1465,1543,1619,1709,1782,1861,1939,2020"}, "to": {"startLines": "34,50,70,72,73,75,89,90,91,138,139,140,141,146,147,148,149,150,151,152,153,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3081,4622,6828,6970,7039,7183,8233,8304,8385,12259,12343,12432,12504,12910,12993,13069,13149,13231,13310,13388,13464,13655,13728,13807,13885", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3150,4701,6897,7034,7115,7245,8299,8380,8463,12338,12427,12499,12585,12988,13064,13144,13226,13305,13383,13459,13549,13723,13802,13880,13961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "52,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "4817,5248,5348,5462", "endColumns": "104,99,113,101", "endOffsets": "4917,5343,5457,5559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,12590", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,12670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,54,55,56,71,74,76,77,78,79,80,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3155,3233,3311,3396,3493,4312,4408,4538,5017,5084,5152,6902,7120,7250,7358,7418,7484,7540,7611,7671,7725,7851,7908,7970,8024,8099,8468,8553,8631,8726,8811,8892,9029,9113,9199,9332,9423,9501,9557,9612,9678,9752,9830,9901,9983,10055,10132,10212,10286,10393,10486,10559,10651,10747,10821,10897,10993,11045,11127,11194,11281,11368,11430,11494,11557,11627,11733,11849,11946,12060,12120,12179,12675,12758,12835", "endLines": "6,35,36,37,38,39,47,48,49,54,55,56,71,74,76,77,78,79,80,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,143,144,145", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "365,3228,3306,3391,3488,3581,4403,4533,4617,5079,5147,5243,6965,7178,7353,7413,7479,7535,7606,7666,7720,7846,7903,7965,8019,8094,8228,8548,8626,8721,8806,8887,9024,9108,9194,9327,9418,9496,9552,9607,9673,9747,9825,9896,9978,10050,10127,10207,10281,10388,10481,10554,10646,10742,10816,10892,10988,11040,11122,11189,11276,11363,11425,11489,11552,11622,11728,11844,11941,12055,12115,12174,12254,12753,12830,12905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,381,513,641,770,901,1035,1135,1269,1402", "endColumns": "110,94,119,131,127,128,130,133,99,133,132,122", "endOffsets": "161,256,376,508,636,765,896,1030,1130,1264,1397,1520"}, "to": {"startLines": "51,53,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4706,4922,5564,5684,5816,5944,6073,6204,6338,6438,6572,6705", "endColumns": "110,94,119,131,127,128,130,133,99,133,132,122", "endOffsets": "4812,5012,5679,5811,5939,6068,6199,6333,6433,6567,6700,6823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "40,41,42,43,44,45,46,154", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3586,3684,3791,3888,3987,4091,4195,13554", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3679,3786,3883,3982,4086,4190,4307,13650"}}]}]}