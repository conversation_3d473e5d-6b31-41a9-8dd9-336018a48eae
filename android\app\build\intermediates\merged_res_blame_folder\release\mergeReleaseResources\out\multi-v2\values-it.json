{"logs": [{"outputFile": "com.gss.goodkey.app-mergeReleaseResources-48:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0022c038c951d6e7d28f4306e8d52cd5\\transformed\\react-android-0.79.5-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "33,49,69,71,72,87,88,135,136,137,142,143,144,145,146,147,148,149,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,4587,6866,7022,7094,8224,8304,12202,12292,12373,12789,12875,12952,13032,13111,13186,13256,13325,13516,13591,13672", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "3077,4666,6932,7089,7177,8299,8383,12287,12368,12456,12870,12947,13027,13106,13181,13251,13320,13410,13586,13667,13754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "39,40,41,42,43,44,45,150", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3518,3616,3718,3817,3919,4028,4135,13415", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3611,3713,3812,3914,4023,4130,4260,13511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,12461", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,12538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3082,3166,3247,3324,3423,4265,4364,4504,4977,5041,5107,6937,7182,7244,7332,7394,7463,7526,7599,7662,7716,7837,7894,7956,8010,8087,8388,8473,8553,8652,8738,8820,8955,9036,9117,9263,9354,9444,9499,9550,9616,9689,9769,9840,9920,9995,10072,10141,10218,10323,10411,10500,10593,10686,10760,10840,10934,10985,11069,11135,11219,11307,11369,11433,11496,11564,11679,11793,11899,12008,12067,12122,12543,12628,12707", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,74,75,76,77,78,79,80,81,82,83,84,85,86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,139,140,141", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "313,3161,3242,3319,3418,3513,4359,4499,4582,5036,5102,5197,7017,7239,7327,7389,7458,7521,7594,7657,7711,7832,7889,7951,8005,8082,8219,8468,8548,8647,8733,8815,8950,9031,9112,9258,9349,9439,9494,9545,9611,9684,9764,9835,9915,9990,10067,10136,10213,10318,10406,10495,10588,10681,10755,10835,10929,10980,11064,11130,11214,11302,11364,11428,11491,11559,11674,11788,11894,12003,12062,12117,12197,12623,12702,12784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,261,374,509,655,811,941,1099,1201,1338,1489", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "161,256,369,504,650,806,936,1094,1196,1333,1484,1609"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4671,4882,5513,5626,5761,5907,6063,6193,6351,6453,6590,6741", "endColumns": "110,94,112,134,145,155,129,157,101,136,150,124", "endOffsets": "4777,4972,5621,5756,5902,6058,6188,6346,6448,6585,6736,6861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4782,5202,5300,5410", "endColumns": "99,97,109,102", "endOffsets": "4877,5295,5405,5508"}}]}]}