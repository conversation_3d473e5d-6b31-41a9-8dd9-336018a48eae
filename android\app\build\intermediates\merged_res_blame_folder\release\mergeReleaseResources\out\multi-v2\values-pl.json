{"logs": [{"outputFile": "com.gss.goodkey.app-mergeReleaseResources-48:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,12412", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,12490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "40,41,42,43,44,45,46,152", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3576,3673,3775,3873,3972,4086,4191,13366", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3668,3770,3868,3967,4081,4186,4308,13462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "52,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "4778,5178,5277,5392", "endColumns": "99,98,114,103", "endOffsets": "4873,5272,5387,5491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,249,374,512,665,792,920,1067,1167,1301,1440", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "154,244,369,507,660,787,915,1062,1162,1296,1435,1559"}, "to": {"startLines": "51,53,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4674,4878,5496,5621,5759,5912,6039,6167,6314,6414,6548,6687", "endColumns": "103,89,124,137,152,126,127,146,99,133,138,123", "endOffsets": "4773,4963,5616,5754,5907,6034,6162,6309,6409,6543,6682,6806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1163,1227,1308,1372,1433,1544,1608,1676,1730,1799,1861,1915,2026,2087,2149,2203,2275,2404,2493,2572,2667,2752,2834,2983,3065,3148,3285,3372,3449,3503,3554,3620,3691,3767,3838,3921,3998,4076,4154,4230,4338,4428,4501,4596,4693,4765,4839,4939,4991,5076,5142,5230,5320,5382,5446,5509,5580,5687,5799,5898,6005,6063,6118,6194,6278,6355", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1158,1222,1303,1367,1428,1539,1603,1671,1725,1794,1856,1910,2021,2082,2144,2198,2270,2399,2488,2567,2662,2747,2829,2978,3060,3143,3280,3367,3444,3498,3549,3615,3686,3762,3833,3916,3993,4071,4149,4225,4333,4423,4496,4591,4688,4760,4834,4934,4986,5071,5137,5225,5315,5377,5441,5504,5575,5682,5794,5893,6000,6058,6113,6189,6273,6350,6428"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,54,55,56,71,74,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3223,3298,3377,3481,4313,4398,4515,4968,5033,5097,6884,7102,7163,7274,7338,7406,7460,7529,7591,7645,7756,7817,7879,7933,8005,8289,8378,8457,8552,8637,8719,8868,8950,9033,9170,9257,9334,9388,9439,9505,9576,9652,9723,9806,9883,9961,10039,10115,10223,10313,10386,10481,10578,10650,10724,10824,10876,10961,11027,11115,11205,11267,11331,11394,11465,11572,11684,11783,11890,11948,12003,12495,12579,12656", "endLines": "7,35,36,37,38,39,47,48,49,54,55,56,71,74,75,76,77,78,79,80,81,82,83,84,85,86,87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,141,142,143", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "431,3218,3293,3372,3476,3571,4393,4510,4592,5028,5092,5173,6943,7158,7269,7333,7401,7455,7524,7586,7640,7751,7812,7874,7928,8000,8129,8373,8452,8547,8632,8714,8863,8945,9028,9165,9252,9329,9383,9434,9500,9571,9647,9718,9801,9878,9956,10034,10110,10218,10308,10381,10476,10573,10645,10719,10819,10871,10956,11022,11110,11200,11262,11326,11389,11460,11567,11679,11778,11885,11943,11998,12074,12574,12651,12729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0022c038c951d6e7d28f4306e8d52cd5\\transformed\\react-android-0.79.5-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,205,274,359,435,514,597,692,762,847,933,1008,1090,1173,1251,1323,1393,1479,1557,1633,1707", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "127,200,269,354,430,509,592,687,757,842,928,1003,1085,1168,1246,1318,1388,1474,1552,1628,1702,1782"}, "to": {"startLines": "50,70,72,73,88,89,136,137,138,139,144,145,146,147,148,149,150,151,153,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4597,6811,6948,7017,8134,8210,12079,12162,12257,12327,12734,12820,12895,12977,13060,13138,13210,13280,13467,13545,13621,13695", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "4669,6879,7012,7097,8205,8284,12157,12252,12322,12407,12815,12890,12972,13055,13133,13205,13275,13361,13540,13616,13690,13770"}}]}]}