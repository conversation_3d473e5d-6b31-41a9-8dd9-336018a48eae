{"logs": [{"outputFile": "com.gss.goodkey.app-mergeReleaseResources-48:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0022c038c951d6e7d28f4306e8d52cd5\\transformed\\react-android-0.79.5-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,326,401,466,531,600,671,744,819,886,956,1029,1101,1178,1254,1326,1396,1465,1545,1613,1683,1750", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,188,255,321,396,461,526,595,666,739,814,881,951,1024,1096,1173,1249,1321,1391,1460,1540,1608,1678,1745,1814"}, "to": {"startLines": "33,49,69,71,72,74,88,89,90,137,138,139,140,145,146,147,148,149,150,151,152,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2858,4192,6006,6132,6198,6331,7231,7296,7365,10715,10788,10863,10930,11297,11370,11442,11519,11595,11667,11737,11806,11987,12055,12125,12192", "endColumns": "65,71,66,65,74,64,64,68,70,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2919,4259,6068,6193,6268,6391,7291,7360,7431,10783,10858,10925,10995,11365,11437,11514,11590,11662,11732,11801,11881,12050,12120,12187,12256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "39,40,41,42,43,44,45,153", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3261,3353,3452,3546,3640,3733,3826,11886", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3348,3447,3541,3635,3728,3821,3917,11982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2924,2987,3048,3115,3184,3922,4012,4119,4526,4577,4639,6073,6273,6396,6474,6535,6592,6648,6707,6765,6819,6905,6961,7019,7073,7138,7436,7510,7582,7662,7736,7814,7934,7997,8060,8159,8236,8310,8360,8411,8477,8541,8609,8680,8752,8813,8884,8951,9011,9099,9179,9242,9325,9410,9484,9549,9625,9673,9747,9811,9887,9965,10027,10091,10154,10220,10300,10380,10456,10537,10591,10646,11079,11154,11227", "endLines": "5,34,35,36,37,38,46,47,48,53,54,55,70,73,75,76,77,78,79,80,81,82,83,84,85,86,87,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,142,143,144", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "292,2982,3043,3110,3179,3256,4007,4114,4187,4572,4634,4712,6127,6326,6469,6530,6587,6643,6702,6760,6814,6900,6956,7014,7068,7133,7226,7505,7577,7657,7731,7809,7929,7992,8055,8154,8231,8305,8355,8406,8472,8536,8604,8675,8747,8808,8879,8946,9006,9094,9174,9237,9320,9405,9479,9544,9620,9668,9742,9806,9882,9960,10022,10086,10149,10215,10295,10375,10451,10532,10586,10641,10710,11149,11222,11292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cabed006c47875d0605c3a31d6f0c7c2\\transformed\\biometric-1.1.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,234,333,436,538,636,738,840,930,1038,1141", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "148,229,328,431,533,631,733,835,925,1033,1136,1232"}, "to": {"startLines": "50,52,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4264,4445,5003,5102,5205,5307,5405,5507,5609,5699,5807,5910", "endColumns": "97,80,98,102,101,97,101,101,89,107,102,95", "endOffsets": "4357,4521,5097,5200,5302,5400,5502,5604,5694,5802,5905,6001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "51,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4362,4717,4809,4910", "endColumns": "82,91,100,92", "endOffsets": "4440,4804,4905,4998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,11000", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,11074"}}]}]}