com.gss.goodkey.app-react-android-0.79.5-release-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0022c038c951d6e7d28f4306e8d52cd5\transformed\react-android-0.79.5-release\res
com.gss.goodkey.app-profileinstaller-1.3.1-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\res
com.gss.goodkey.app-activity-ktx-1.8.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0778a2aa5b019b2def3eb71c0ed4a154\transformed\activity-ktx-1.8.0\res
com.gss.goodkey.app-lifecycle-runtime-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\09f7c3694aac112487e9f730f6c374d4\transformed\lifecycle-runtime-2.6.2\res
com.gss.goodkey.app-lifecycle-viewmodel-savedstate-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\0a40c827f1ab6cae60b0a38160d029dc\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.gss.goodkey.app-expo.modules.securestore-14.2.3-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\11f5bd776e6bd90607354863cae0358e\transformed\expo.modules.securestore-14.2.3\res
com.gss.goodkey.app-drawerlayout-1.1.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\183e370a152c2b8981bc4a82353880f3\transformed\drawerlayout-1.1.1\res
com.gss.goodkey.app-tracing-ktx-1.2.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\18cdabc45926f1dc3dff94376b7201d0\transformed\tracing-ktx-1.2.0\res
com.gss.goodkey.app-constraintlayout-2.0.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\1b766223c9ad7f2daee8b81f58bc7213\transformed\constraintlayout-2.0.1\res
com.gss.goodkey.app-startup-runtime-1.1.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\1c233a1110e41e54547ee5b84513eb96\transformed\startup-runtime-1.1.1\res
com.gss.goodkey.app-core-runtime-2.2.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\274a5e4c52765583c47c315e8a011727\transformed\core-runtime-2.2.0\res
com.gss.goodkey.app-fragment-1.6.1-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\27ed71059cba4dfa5c0c363f337b269f\transformed\fragment-1.6.1\res
com.gss.goodkey.app-coordinatorlayout-1.2.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\28bc247258e5cff6e6aac54078e1de43\transformed\coordinatorlayout-1.2.0\res
com.gss.goodkey.app-cardview-1.0.0-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ba03b814d30daf0b666892538f8bffe\transformed\cardview-1.0.0\res
com.gss.goodkey.app-core-1.13.1-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\res
com.gss.goodkey.app-appcompat-1.7.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\4bf03df1e227318d4ab7c4ce04613cbc\transformed\appcompat-1.7.0\res
com.gss.goodkey.app-emoji2-views-helper-1.3.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\51166ce65ea68e12719e2bca48ab2cfd\transformed\emoji2-views-helper-1.3.0\res
com.gss.goodkey.app-lifecycle-viewmodel-2.6.2-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\58726736d2c8b2cb18170698e2d2af0c\transformed\lifecycle-viewmodel-2.6.2\res
com.gss.goodkey.app-recyclerview-1.1.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\59364def5c2f903fdc1dceeb75244942\transformed\recyclerview-1.1.0\res
com.gss.goodkey.app-emoji2-1.3.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\res
com.gss.goodkey.app-viewpager2-1.0.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\6bc00ab5eaca23c3c3ebd49ed8511ff9\transformed\viewpager2-1.0.0\res
com.gss.goodkey.app-drawee-3.6.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\6fcecc4c6bb8b8753bdcf3c3459c4fbc\transformed\drawee-3.6.0\res
com.gss.goodkey.app-material-1.12.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\7a9086f53045ff51dd925c6f1785f9fd\transformed\material-1.12.0\res
com.gss.goodkey.app-lifecycle-livedata-core-2.6.2-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\7c99fc93f13e5345aee29eaa313ee725\transformed\lifecycle-livedata-core-2.6.2\res
com.gss.goodkey.app-lifecycle-livedata-core-ktx-2.6.2-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4851cc4faabf79f6af50acd560b500\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.gss.goodkey.app-savedstate-ktx-1.2.1-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\829cfe53c64cfc693496f8451f29e7a8\transformed\savedstate-ktx-1.2.1\res
com.gss.goodkey.app-lifecycle-viewmodel-ktx-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\850320aa2b4b52ced6f3465503644aaa\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.gss.goodkey.app-lifecycle-livedata-2.6.2-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\86cd36f2af6251908c1cd91569dccf65\transformed\lifecycle-livedata-2.6.2\res
com.gss.goodkey.app-expo.modules.filesystem-18.1.11-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\res
com.gss.goodkey.app-annotation-experimental-1.4.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\8f12b21da55a4e5824d164fe3a97fe6c\transformed\annotation-experimental-1.4.0\res
com.gss.goodkey.app-swiperefreshlayout-1.1.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\a177d82bc21b186c301bc9b6c8d7387d\transformed\swiperefreshlayout-1.1.0\res
com.gss.goodkey.app-activity-1.8.0-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\a4309c375f7c655b9a04946660dc90cc\transformed\activity-1.8.0\res
com.gss.goodkey.app-lifecycle-service-2.6.2-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\b153c5f07cea60c340fe199e7ef9098d\transformed\lifecycle-service-2.6.2\res
com.gss.goodkey.app-autofill-1.1.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\b52e52fc777190b4295f59d0a858517f\transformed\autofill-1.1.0\res
com.gss.goodkey.app-lifecycle-process-2.6.2-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\res
com.gss.goodkey.app-fragment-ktx-1.6.1-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\bd599e307399f0c0086eaf5c5077a9e1\transformed\fragment-ktx-1.6.1\res
com.gss.goodkey.app-transition-1.5.0-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\c2aaf2f0c5250c0e148ab60ada39c9da\transformed\transition-1.5.0\res
com.gss.goodkey.app-biometric-1.1.0-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\cabed006c47875d0605c3a31d6f0c7c2\transformed\biometric-1.1.0\res
com.gss.goodkey.app-appcompat-resources-1.7.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\cafe38d429a8d27ed8b0af5abbc941f2\transformed\appcompat-resources-1.7.0\res
com.gss.goodkey.app-lifecycle-runtime-ktx-2.6.2-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\de145cd6b45e8b1fb7e804d6c000ef76\transformed\lifecycle-runtime-ktx-2.6.2\res
com.gss.goodkey.app-browser-1.6.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\dfa8d219fb756bbf0447e8d2f088c459\transformed\browser-1.6.0\res
com.gss.goodkey.app-media-1.0.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\e2740c2d15f5f0e1c1cc73d5e9e0b8e0\transformed\media-1.0.0\res
com.gss.goodkey.app-tracing-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\f7ab8f7120f36931c3468a226fbeffc5\transformed\tracing-1.2.0\res
com.gss.goodkey.app-core-ktx-1.13.1-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\fbaea5332f0cb03d7c9e16969244d33b\transformed\core-ktx-1.13.1\res
com.gss.goodkey.app-savedstate-1.2.1-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc1cc582935f0fa4fa7ad13b3b8686c\transformed\savedstate-1.2.1\res
com.gss.goodkey.app-res-45 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\generated\res\createBundleReleaseJsAndAssets
com.gss.goodkey.app-pngs-46 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\generated\res\pngs\release
com.gss.goodkey.app-resValues-47 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\generated\res\resValues\release
com.gss.goodkey.app-packageReleaseResources-48 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.gss.goodkey.app-packageReleaseResources-49 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.gss.goodkey.app-release-50 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\build\intermediates\merged_res\release\mergeReleaseResources
com.gss.goodkey.app-main-51 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\main\res
com.gss.goodkey.app-release-52 C:\Users\<USER>\Documents\augment-projects\mobile_app\android\app\src\release\res
com.gss.goodkey.app-release-53 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-54 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-constants\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-55 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-client\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-56 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-launcher\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-57 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu-interface\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-58 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-dev-menu\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-59 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-json-utils\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-60 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-manifests\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-61 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-modules-core\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-62 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-63 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\expo\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-64 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-65 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\react-native-mmkv\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-66 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-67 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-68 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\react-native-screens\android\build\intermediates\packaged_res\release\packageReleaseResources
com.gss.goodkey.app-release-69 C:\Users\<USER>\Documents\augment-projects\mobile_app\node_modules\react-native-svg\android\build\intermediates\packaged_res\release\packageReleaseResources
