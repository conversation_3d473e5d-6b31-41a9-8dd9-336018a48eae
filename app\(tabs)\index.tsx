import React, { useState, useMemo } from 'react';
import { View, Text, ScrollView, RefreshControl, TouchableOpacity, TextInput, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { useAuthStore } from '../../src/stores/authStore';
import { useShows } from '../../src/hooks/useApi';
import { ShowInList } from '../../src/models/Show';
import { Card, CardContent } from '../../src/components/ui/Card';
import { Button } from '../../src/components/ui/Button';
import { Logo } from '../../src/components/ui/Logo';
import { 
  Calendar, 
  MapPin, 
  Clock, 
  ChevronRight, 
  TrendingUp, 
  Eye, 
  Search,
  Grid3X3,
  List,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Sparkles,
  Star,
  Users,
  Activity,
  Zap,
  PlayCircle,
  Timer,
  Building2,
  Archive
} from 'lucide-react-native';

type ViewMode = 'list' | 'grid';

export default function HomeScreen() {
  const { user } = useAuthStore();
  const { data: shows, isLoading, refetch, isRefetching, error } = useShows();

  console.log("Shows data:", shows);
  console.log("Shows loading:", isLoading);
  console.log("Shows error:", error);
  console.log("API URL:", process.env.EXPO_PUBLIC_API_URL);
  
  // State for search and view mode
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('list');

  // Filtered shows - only show active shows (not archived and published)
  const activeShows = useMemo(() => {
    if (!shows) return [];
    
    let filtered = shows.filter((show) => {
      // Only show active shows (not archived and published)
      const isActive = !show.archive && show.display;
      
      // Search filter
      const matchesSearch = searchQuery === '' || 
        show.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        show.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        show.locationName.toLowerCase().includes(searchQuery.toLowerCase());
      
      return isActive && matchesSearch;
    });
    
    // Sort by start date
    filtered.sort((a, b) => {
      return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
    });
    
    return filtered;
  }, [shows, searchQuery]);

  const handleShowPress = (showId: number) => {
    router.push(`/show/${showId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatDateLong = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  const getShowStatus = (show: ShowInList) => {
    const now = new Date();
    const startDate = new Date(show.startDate);
    const endDate = new Date(show.endDate);
    const orderDeadline = new Date(show.orderDeadlineDate);
    
    if (show.archive) return { status: 'archived', color: 'gray', icon: Archive };
    if (!show.display) return { status: 'draft', color: 'yellow', icon: AlertCircle };
    if (now > endDate) return { status: 'completed', color: 'blue', icon: CheckCircle2 };
    if (now > orderDeadline) return { status: 'deadline_passed', color: 'red', icon: XCircle };
    if (now >= startDate) return { status: 'ongoing', color: 'green', icon: CheckCircle2 };
    return { status: 'upcoming', color: 'blue', icon: Clock };
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'archived': return 'Archived';
      case 'draft': return 'Draft';
      case 'completed': return 'Completed';
      case 'deadline_passed': return 'Deadline Passed';
      case 'ongoing': return 'Ongoing';
      case 'upcoming': return 'Upcoming';
      default: return 'Unknown';
    }
  };



  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <SafeAreaView className="flex-1 bg-white">
        <ScrollView
          className="flex-1"
          refreshControl={
            <RefreshControl
              refreshing={isRefetching}
              onRefresh={refetch}
              tintColor="#005B63"
              colors={['#005B63']}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Clean Header */}
          <View className="px-6 pt-6 pb-8 bg-white">
            {/* Logo */}
            <View className="items-center mb-6">
              <Logo size="md" />
            </View>

            <View className="flex-row items-center justify-between mb-8">
              <View className="flex-1">
                <Text className="text-sm font-medium text-gray-500 mb-2">
                  Good {new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening'}
                </Text>
                <Text className="text-3xl font-bold text-gray-900 mb-1">
                  {user?.name?.split(' ')[0] || 'Welcome'}
                </Text>
                <Text className="text-gray-600 text-base">
                  {activeShows.length} active show{activeShows.length !== 1 ? 's' : ''} • {shows?.length || 0} total
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                className="bg-gray-100 p-3 rounded-2xl"
              >
                {viewMode === 'list' ? (
                  <Grid3X3 size={22} color="#374151" />
                ) : (
                  <List size={22} color="#374151" />
                )}
              </TouchableOpacity>
            </View>

            {/* Search Bar */}
            <View className="bg-gray-50 rounded-2xl p-4 mb-6">
              <View className="flex-row items-center">
                <Search size={20} color="#9ca3af" />
                <TextInput
                  className="flex-1 ml-3 text-gray-900 text-base"
                  placeholder="Search shows..."
                  placeholderTextColor="#9ca3af"
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity onPress={() => setSearchQuery('')}>
                    <XCircle size={20} color="#9ca3af" />
                  </TouchableOpacity>
                )}
              </View>
            </View>

            {/* Quick Stats */}
            <View className="flex-row gap-4 mb-8">
              <View className="flex-1 bg-emerald-50 rounded-2xl p-4">
                <View className="flex-row items-center justify-between">
                  <View>
                    <Text className="text-2xl font-bold text-emerald-600">{activeShows.length}</Text>
                    <Text className="text-emerald-600 text-sm font-medium">Active Shows</Text>
                  </View>
                  <View className="bg-emerald-100 p-2 rounded-xl">
                    <Activity size={18} color="#059669" />
                  </View>
                </View>
              </View>
              
              <View className="flex-1 bg-blue-50 rounded-2xl p-4">
                <View className="flex-row items-center justify-between">
                  <View>
                    <Text className="text-2xl font-bold text-blue-600">{shows?.length || 0}</Text>
                    <Text className="text-blue-600 text-sm font-medium">Total Shows</Text>
                  </View>
                  <View className="bg-blue-100 p-2 rounded-xl">
                    <Star size={18} color="#2563eb" />
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/* Shows Content */}
          <View className="px-6">
            {isLoading ? (
              <View className="flex flex-col gap-4">
                {[1, 2, 3].map((i) => (
                  <View key={i} className="bg-gray-50 rounded-3xl p-6">
                    <View className="animate-pulse">
                      <View className="flex-row items-center justify-between mb-4">
                        <View className="h-6 bg-gray-200 rounded-2xl w-2/3" />
                        <View className="h-8 bg-gray-200 rounded-full w-20" />
                      </View>
                      <View className="h-4 bg-gray-200 rounded-xl w-1/3 mb-4" />
                      <View className="flex flex-col gap-3">
                        <View className="h-4 bg-gray-200 rounded-xl w-full" />
                        <View className="h-4 bg-gray-200 rounded-xl w-3/4" />
                        <View className="h-4 bg-gray-200 rounded-xl w-1/2" />
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            ) : error ? (
              <Card className="bg-white shadow-lg border-0" variant="elevated">
                <CardContent className="p-12">
                  <View className="items-center">
                    <View className="bg-red-100 p-8 rounded-full mb-6">
                      <AlertCircle size={48} color="#ef4444" />
                    </View>
                    <Text className="text-2xl font-bold text-slate-900 mb-3">
                      Something went wrong
                    </Text>
                    <Text className="text-slate-600 text-center leading-6 mb-8 text-base">
                      We couldn't load your shows. Please try again.
                    </Text>
                    <Button
                      title="Try Again"
                      variant="outline"
                      className="px-8 py-4"
                      onPress={() => refetch()}
                    />
                  </View>
                </CardContent>
              </Card>
            ) : activeShows.length > 0 ? (
              <View className={viewMode === 'grid' ? 'flex-row flex-wrap gap-4' : 'flex flex-col gap-4'}>
                {activeShows.map((show) => {
                  const showStatus = getShowStatus(show);
                  const StatusIcon = showStatus.icon;
                  const now = new Date();
                  const startDate = new Date(show.startDate);
                  const daysUntilStart = Math.ceil((startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
                  
                  return viewMode === 'grid' ? (
                    // Clean Grid View
                    <View key={show.id} className="w-[calc(50%-8px)]">
                      <TouchableOpacity
                        onPress={() => handleShowPress(show.id)}
                        activeOpacity={0.7}
                        className="bg-white rounded-3xl p-5 border border-gray-100 h-full"
                      >
                        {/* Status Badge */}
                        <View className="flex-row items-center justify-between mb-4">
                          <View className={`px-3 py-1 rounded-full ${
                            showStatus.color === 'green' ? 'bg-emerald-100' :
                            showStatus.color === 'blue' ? 'bg-blue-100' :
                            showStatus.color === 'red' ? 'bg-red-100' : 'bg-gray-100'
                          }`}>
                            <Text className={`text-xs font-semibold ${
                              showStatus.color === 'green' ? 'text-emerald-700' :
                              showStatus.color === 'blue' ? 'text-blue-700' :
                              showStatus.color === 'red' ? 'text-red-700' : 'text-gray-700'
                            }`}>
                              {getStatusText(showStatus.status)}
                            </Text>
                          </View>
                          <StatusIcon size={16} color={
                            showStatus.color === 'green' ? '#059669' :
                            showStatus.color === 'blue' ? '#2563eb' :
                            showStatus.color === 'red' ? '#dc2626' : '#6b7280'
                          } />
                        </View>

                        {/* Show Info */}
                        <View className="flex-1 flex flex-col gap-4">
                          <View>
                            <Text className="text-lg font-bold text-gray-900 mb-2" numberOfLines={2}>
                              {show.name}
                            </Text>
                            <Text className="text-sm text-gray-500 font-mono">
                              {show.code}
                            </Text>
                          </View>

                          <View className="flex flex-col gap-3">
                            <View className="flex-row items-center gap-2">
                              <Calendar size={16} color="#6b7280" />
                              <Text className="text-sm text-gray-600" numberOfLines={1}>
                                {formatDateLong(show.startDate)}
                              </Text>
                            </View>
                            
                            <View className="flex-row items-center gap-2">
                              <Building2 size={16} color="#6b7280" />
                              <Text className="text-sm text-gray-600" numberOfLines={1}>
                                {show.locationName}
                              </Text>
                            </View>
                          </View>

                          {daysUntilStart > 0 && daysUntilStart <= 30 && (
                            <View className="bg-amber-50 rounded-2xl p-3">
                              <Text className="text-amber-800 text-xs font-semibold text-center">
                                {daysUntilStart} day{daysUntilStart !== 1 ? 's' : ''} to go
                              </Text>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                  ) : (
                    // Clean List View
                    
                    <TouchableOpacity
                      key={show.id}
                      onPress={() => handleShowPress(show.id)}
                      activeOpacity={0.7}
                      className="bg-white rounded-3xl px-4 py-4 border border-gray-100"
                    >
                      {/* Header */}
                      <View className="flex-row items-start justify-between mb-4">
                        <View className="flex-1 pr-4">
                          <View className="flex-row items-center justify-between mb-2">
                            <Text className="text-xl font-bold text-gray-900 flex-1" numberOfLines={1}>
                              {show.name}
                            </Text>
                            <View className={`px-3 py-1 rounded-full ml-3 ${
                              showStatus.color === 'green' ? 'bg-emerald-100' :
                              showStatus.color === 'blue' ? 'bg-blue-100' :
                              showStatus.color === 'red' ? 'bg-red-100' : 'bg-gray-100'
                            }`}>
                              <Text className={`text-xs font-semibold ${
                                showStatus.color === 'green' ? 'text-emerald-700' :
                                showStatus.color === 'blue' ? 'text-blue-700' :
                                showStatus.color === 'red' ? 'text-red-700' : 'text-gray-700'
                              }`}>
                                {getStatusText(showStatus.status)}
                              </Text>
                            </View>
                          </View>
                          <Text className="text-sm text-gray-500 font-mono">
                            {show.code}
                          </Text>
                        </View>
                        <ChevronRight size={20} color="#9ca3af" />
                      </View>

                      {show.description && (
                        <Text className="text-gray-600 mb-4 leading-5" numberOfLines={2}>
                          {show.description}
                        </Text>
                      )}

                      {/* Info Grid */}
                      <View className="flex flex-col gap-3">
                        <View className="bg-gray-50 rounded-2xl p-4">
                          <View className="flex-row items-center mb-2">
                            <Calendar size={18} color="#6b7280" />
                            <Text className="text-gray-700 font-semibold ml-3">Event Period</Text>
                          </View>
                          <Text className="text-gray-900 font-bold text-base">
                            {formatDate(show.startDate)} - {formatDate(show.endDate)}
                          </Text>
                        </View>

                        <View className="flex-row gap-3">
                          <View className="flex-1 bg-gray-50 rounded-2xl p-4">
                            <View className="flex-row items-center mb-2">
                              <Building2 size={16} color="#6b7280" />
                              <Text className="text-gray-700 font-semibold text-sm ml-2">Venue</Text>
                            </View>
                            <Text className="text-gray-900 font-bold text-sm" numberOfLines={1}>
                              {show.locationName}
                            </Text>
                          </View>

                          <View className="flex-1 bg-gray-50 rounded-2xl p-4">
                            <View className="flex-row items-center mb-2">
                              <Timer size={16} color="#6b7280" />
                              <Text className="text-gray-700 font-semibold text-sm ml-2">Deadline</Text>
                            </View>
                            <Text className="text-gray-900 font-bold text-sm" numberOfLines={1}>
                              {formatDate(show.orderDeadlineDate)}
                            </Text>
                          </View>
                        </View>

                        {daysUntilStart > 0 && daysUntilStart <= 30 ? (
                          <View className="bg-blue-50 rounded-2xl p-4">
                            <View className="flex-row items-center">
                              <Zap size={18} color="#2563eb" />
                              <Text className="text-blue-800 font-bold text-base ml-3">
                                {daysUntilStart} day{daysUntilStart !== 1 ? 's' : ''} until showtime!
                              </Text>
                            </View>
                          </View>
                        ) : show.lateChargePercentage && parseFloat(show.lateChargePercentage) > 0 && (
                          <View className="bg-amber-50 rounded-2xl p-4">
                            <View className="flex-row items-center">
                              <AlertCircle size={18} color="#d97706" />
                              <Text className="text-amber-800 font-bold text-base ml-3">
                                Late charge: {show.lateChargePercentage}%
                              </Text>
                            </View>
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            ) : searchQuery ? (
              <Card className="bg-white shadow-lg border-0" variant="elevated">
                <CardContent className="p-12">
                  <View className="items-center">
                    <View className="bg-slate-100 p-8 rounded-full mb-6">
                      <Search size={48} color="#64748b" />
                    </View>
                    <Text className="text-2xl font-bold text-slate-900 mb-3">
                      No matches found
                    </Text>
                    <Text className="text-slate-600 text-center leading-6 mb-8 text-base">
                      We couldn't find any active shows matching "{searchQuery}". Try a different search term.
                    </Text>
                    <Button
                      title="Clear Search"
                      variant="outline"
                      className="px-8 py-4"
                      onPress={() => setSearchQuery('')}
                    />
                  </View>
                </CardContent>
              </Card>
            ) : (
              <Card className="bg-white shadow-lg border-0" variant="elevated">
                <CardContent className="p-12">
                  <View className="items-center">
                    <View className="bg-gradient-to-br from-slate-100 to-slate-200 p-8 rounded-full mb-6">
                      <PlayCircle size={48} color="#64748b" />
                    </View>
                    <Text className="text-2xl font-bold text-slate-900 mb-3">
                      Ready to get started?
                    </Text>
                    <Text className="text-slate-600 text-center leading-6 mb-8 text-base">
                      Your active shows will appear here once they're published. Create your first show to get the party started!
                    </Text>
                    <Button
                      title="Create Your First Show"
                      variant="outline"
                      className="px-8 py-4"
                      onPress={() => {/* Add navigation to create show */}}
                    />
                  </View>
                </CardContent>
              </Card>
            )}
          </View>

          <View className="h-20" />
        </ScrollView>
      </SafeAreaView>
    </>
  );
}
