import React from 'react';
import { View, Text, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '../../src/stores/authStore';
import { Card, CardContent, CardHeader } from '../../src/components/ui/Card';
import { Button } from '../../src/components/ui/Button';
import { User, Mail, Phone, MapPin, Calendar, Edit } from 'lucide-react-native';

export default function ProfileScreen() {
  const { user, signOut } = useAuthStore();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="px-4 py-6 bg-white border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-900">Profile</Text>
        </View>

        {/* Profile Info */}
        <View className="px-4 py-6">
          <Card variant="elevated">
            <CardContent>
              <View className="items-center">
                {/* Avatar */}
                <View className="w-24 h-24 rounded-full bg-primary-100 items-center justify-center mb-4">
                  {user?.avatar ? (
                    <Image
                      source={{ uri: user.avatar }}
                      className="w-24 h-24 rounded-full"
                    />
                  ) : (
                    <User size={40} color="#3b82f6" />
                  )}
                </View>

                {/* Name and Email */}
                <Text className="text-xl font-bold text-gray-900 mb-1">
                  {user?.name || 'User Name'}
                </Text>
                <Text className="text-gray-600 mb-4">
                  {user?.email || '<EMAIL>'}
                </Text>

                {/* Edit Profile Button */}
                <Button
                  title="Edit Profile"
                  variant="outline"
                  size="sm"
                  onPress={() => {}}
                  className="flex-row items-center"
                >
                  <Edit size={16} color="#3b82f6" />
                  <Text className="ml-2 text-primary-600">Edit Profile</Text>
                </Button>
              </View>
            </CardContent>
          </Card>
        </View>

        {/* Profile Details */}
        <View className="px-4">
          <Card variant="elevated">
            <CardHeader>
              <Text className="text-lg font-semibold text-gray-900">
                Personal Information
              </Text>
            </CardHeader>
            <CardContent>
              <View className="space-y-4">
                <View className="flex-row items-center">
                  <Mail size={20} color="#6b7280" />
                  <View className="ml-3 flex-1">
                    <Text className="text-sm text-gray-600">Email</Text>
                    <Text className="text-gray-900">
                      {user?.email || '<EMAIL>'}
                    </Text>
                  </View>
                </View>

                <View className="flex-row items-center">
                  <Phone size={20} color="#6b7280" />
                  <View className="ml-3 flex-1">
                    <Text className="text-sm text-gray-600">Phone</Text>
                    <Text className="text-gray-900">+****************</Text>
                  </View>
                </View>

                <View className="flex-row items-center">
                  <MapPin size={20} color="#6b7280" />
                  <View className="ml-3 flex-1">
                    <Text className="text-sm text-gray-600">Location</Text>
                    <Text className="text-gray-900">San Francisco, CA</Text>
                  </View>
                </View>

                <View className="flex-row items-center">
                  <Calendar size={20} color="#6b7280" />
                  <View className="ml-3 flex-1">
                    <Text className="text-sm text-gray-600">Member since</Text>
                    <Text className="text-gray-900">January 2024</Text>
                  </View>
                </View>
              </View>
            </CardContent>
          </Card>
        </View>

        {/* Stats */}
        <View className="px-4 py-4">
          <Card variant="elevated">
            <CardHeader>
              <Text className="text-lg font-semibold text-gray-900">
                Activity Stats
              </Text>
            </CardHeader>
            <CardContent>
              <View className="flex-row justify-around">
                <View className="items-center">
                  <Text className="text-2xl font-bold text-primary-600">24</Text>
                  <Text className="text-sm text-gray-600">Posts</Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl font-bold text-green-600">156</Text>
                  <Text className="text-sm text-gray-600">Likes</Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl font-bold text-blue-600">89</Text>
                  <Text className="text-sm text-gray-600">Comments</Text>
                </View>
                <View className="items-center">
                  <Text className="text-2xl font-bold text-purple-600">12</Text>
                  <Text className="text-sm text-gray-600">Followers</Text>
                </View>
              </View>
            </CardContent>
          </Card>
        </View>

        {/* Actions */}
        <View className="px-4 py-4 space-y-3">
          <Button
            title="Change Password"
            variant="outline"
            onPress={() => {}}
          />
          <Button
            title="Privacy Settings"
            variant="outline"
            onPress={() => {}}
          />
          <Button
            title="Help & Support"
            variant="outline"
            onPress={() => {}}
          />
          <Button
            title="Sign Out"
            variant="outline"
            onPress={handleSignOut}
            className="border-red-300 bg-red-50"
          >
            <Text className="text-red-600 font-semibold">Sign Out</Text>
          </Button>
        </View>

        <View className="h-20" />
      </ScrollView>
    </SafeAreaView>
  );
}
