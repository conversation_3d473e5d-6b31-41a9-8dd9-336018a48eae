import React from 'react';
import { View, Text, ScrollView, Switch, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppStore } from '../../src/stores/appStore';
import { useNotificationStore } from '../../src/stores/notificationStore';
import { Card, CardContent, CardHeader } from '../../src/components/ui/Card';
import { Button } from '../../src/components/ui/Button';
import { 
  Moon, 
  Sun, 
  Globe, 
  Bell, 
  Shield, 
  HelpCircle, 
  Info, 
  ChevronRight,
  Smartphone
} from 'lucide-react-native';

export default function SettingsScreen() {
  const { theme, setTheme, language, setLanguage, resetApp } = useAppStore();
  const { isEnabled: notificationsEnabled, setEnabled: setNotificationsEnabled } = useNotificationStore();

  const settingSections = [
    {
      title: 'Appearance',
      items: [
        {
          icon: theme === 'dark' ? Moon : Sun,
          title: 'Theme',
          subtitle: `Current: ${theme}`,
          onPress: () => {
            const themes = ['light', 'dark', 'system'] as const;
            const currentIndex = themes.indexOf(theme);
            const nextTheme = themes[(currentIndex + 1) % themes.length];
            setTheme(nextTheme);
          },
        },
        {
          icon: Globe,
          title: 'Language',
          subtitle: 'English',
          onPress: () => {},
        },
      ],
    },
    {
      title: 'Notifications',
      items: [
        {
          icon: Bell,
          title: 'Push Notifications',
          subtitle: 'Receive notifications on your device',
          hasSwitch: true,
          switchValue: notificationsEnabled,
          onSwitchChange: setNotificationsEnabled,
        },
        {
          icon: Smartphone,
          title: 'Notification Settings',
          subtitle: 'Customize notification preferences',
          onPress: () => {},
        },
      ],
    },
    {
      title: 'Privacy & Security',
      items: [
        {
          icon: Shield,
          title: 'Privacy Settings',
          subtitle: 'Control your privacy preferences',
          onPress: () => {},
        },
        {
          icon: Shield,
          title: 'Security',
          subtitle: 'Manage your account security',
          onPress: () => {},
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          icon: HelpCircle,
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          onPress: () => {},
        },
        {
          icon: Info,
          title: 'About',
          subtitle: 'App version and information',
          onPress: () => {},
        },
      ],
    },
  ];

  const SettingItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      onPress={item.onPress}
      disabled={item.hasSwitch}
      className="flex-row items-center py-4 px-4"
    >
      <View className="w-10 h-10 rounded-full bg-gray-100 items-center justify-center mr-3">
        <item.icon size={20} color="#6b7280" />
      </View>
      
      <View className="flex-1">
        <Text className="text-base font-medium text-gray-900">
          {item.title}
        </Text>
        <Text className="text-sm text-gray-600 mt-1">
          {item.subtitle}
        </Text>
      </View>

      {item.hasSwitch ? (
        <Switch
          value={item.switchValue}
          onValueChange={item.onSwitchChange}
          trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
          thumbColor={item.switchValue ? '#ffffff' : '#ffffff'}
        />
      ) : (
        <ChevronRight size={20} color="#9ca3af" />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="px-4 py-6 bg-white border-b border-gray-200">
          <Text className="text-2xl font-bold text-gray-900">Settings</Text>
        </View>

        {/* Settings Sections */}
        <View className="px-4 py-4">
          {settingSections.map((section, sectionIndex) => (
            <View key={section.title} className="mb-6">
              <Text className="text-sm font-medium text-gray-600 uppercase tracking-wide mb-3 px-4">
                {section.title}
              </Text>
              <Card variant="elevated">
                <CardContent className="p-0">
                  {section.items.map((item, itemIndex) => (
                    <View key={item.title}>
                      <SettingItem item={item} />
                      {itemIndex < section.items.length - 1 && (
                        <View className="h-px bg-gray-200 ml-16" />
                      )}
                    </View>
                  ))}
                </CardContent>
              </Card>
            </View>
          ))}
        </View>

        {/* App Info */}
        <View className="px-4 py-4">
          <Card variant="elevated">
            <CardHeader>
              <Text className="text-lg font-semibold text-gray-900">
                App Information
              </Text>
            </CardHeader>
            <CardContent>
              <View className="space-y-2">
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Version</Text>
                  <Text className="text-gray-900">1.0.0</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Build</Text>
                  <Text className="text-gray-900">100</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Last Updated</Text>
                  <Text className="text-gray-900">Jan 15, 2024</Text>
                </View>
              </View>
            </CardContent>
          </Card>
        </View>

        {/* Danger Zone */}
        <View className="px-4 py-4">
          <Card variant="elevated" className="border-red-200">
            <CardHeader>
              <Text className="text-lg font-semibold text-red-600">
                Danger Zone
              </Text>
            </CardHeader>
            <CardContent>
              <Text className="text-gray-600 mb-4">
                These actions cannot be undone. Please be careful.
              </Text>
              <Button
                title="Reset App Data"
                variant="outline"
                onPress={() => {
                  resetApp();
                  // You might want to show a confirmation dialog here
                }}
                className="border-red-300 bg-red-50"
              >
                <Text className="text-red-600 font-semibold">Reset App Data</Text>
              </Button>
            </CardContent>
          </Card>
        </View>

        <View className="h-20" />
      </ScrollView>
    </SafeAreaView>
  );
}
