import React, { useState } from 'react';
import { View, Text, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { useAuthStore } from '../src/stores/authStore';
import { FormInput } from '../src/components/forms/FormInput';
import { Button } from '../src/components/ui/Button';
import { Card, CardContent, CardHeader } from '../src/components/ui/Card';
import { Lock, User, Eye, EyeOff } from 'lucide-react-native';
import AuthQuery from '../src/services/queries/AuthQuery';
import LoginSchema, { LoginData } from '../src/schemas/Login';
import { Logo } from '../src/components/ui/Logo';

export default function AuthScreen() {
  const [showPassword, setShowPassword] = useState(false);
  const { isLoading } = useAuthStore();

  // AuthQuery login form (username-based)
  const loginForm = useForm<LoginData>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });



  // AuthQuery-based login handler
  const handleAuthQueryLogin = async (data: LoginData) => {
    console.log('=== AuthQuery Login Submit ===');
    console.log('Login data:', data);

    // Set loading state
    useAuthStore.setState({ isLoading: true, error: null });

    try {
      console.log('Calling AuthQuery.Login...');
      const tokens = await AuthQuery.Login(data);
      console.log('AuthQuery.Login response (tokens):', tokens);

      if (tokens && tokens.accessToken && tokens.refreshToken) {
        console.log('Login successful, tokens received:', tokens);

        // First, store the tokens so the fetcher can use them for authenticated requests
        console.log('Storing tokens in auth store...');
        useAuthStore.setState({
          tokens: tokens,
          isLoading: true, // Keep loading while fetching user data
          error: null,
        });

        // Verify tokens are stored
        const storedTokens = useAuthStore.getState().tokens;
        console.log('Tokens stored in auth store:', storedTokens ? { hasAccessToken: !!storedTokens.accessToken, hasRefreshToken: !!storedTokens.refreshToken } : 'null');

        // Add a small delay to ensure tokens are persisted
        await new Promise(resolve => setTimeout(resolve, 100));

        // Now fetch user data with the stored tokens
        console.log('Fetching user data with stored tokens...');
        const userData = await AuthQuery.me();
        console.log('User data response:', userData);

        if (userData) {
          console.log('User data received:', userData);

          // Map UserData to User format expected by auth store
          const user = {
            id: userData.username, // Use username as id since UserData doesn't have id
            email: userData.email,
            name: userData.name,
            avatar: undefined, // UserData doesn't have avatar field
          };

          console.log('Mapped user data:', user);

          // Update auth store with user data and set authenticated
          useAuthStore.setState({
            user: user,
            tokens: tokens, // Keep the tokens
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          console.log('Authentication complete! User is now logged in.');

          // Redirect to main app after successful login
          router.replace('/(tabs)');
        } else {
          throw new Error('Failed to fetch user data');
        }
      } else {
        throw new Error('No valid tokens received from login');
      }

    } catch (error) {
      console.error('AuthQuery Login error:', error);

      // Update auth store with error state
      useAuthStore.setState({
        error: error instanceof Error ? error.message : 'Login failed',
        isLoading: false,
        isAuthenticated: false,
      });
    }
  };





  return (
    <SafeAreaView className="flex-1 bg-background">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1" contentContainerStyle={{ flexGrow: 1 }}>
          <View className="flex-1 justify-center px-4 py-8">
            {/* Header */}
            <View className="items-center mb-8">
              <View className="mb-6">
                <Logo size="xl"  />
              </View>
           
              <Text className="text-muted-foreground text-center">
                Sign in to your account
              </Text>
            </View>

            {/* Auth Form */}
            <Card variant="elevated" className="mb-6">
              <CardHeader>
                <Text className="text-xl font-semibold text-card-foreground text-center">
                  Sign In
                </Text>
              </CardHeader>
              <CardContent>
                <View className="gap-4">
                  <FormInput
                    name="username"
                    control={loginForm.control}
                    label="Username"
                    placeholder="Enter your username"
                    autoCapitalize="none"
                    leftIcon={<User size={20} color="#9C93AF" />}
                  />
                  <FormInput
                    name="password"
                    control={loginForm.control}
                    label="Password"
                    placeholder="Enter your password"
                    secureTextEntry={!showPassword}
                    leftIcon={<Lock size={20} color="#9C93AF" />}
                    rightIcon={
                      <Button
                        title=""
                        variant="ghost"
                        size="sm"
                        onPress={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff size={20} color="#9C93AF" />
                        ) : (
                          <Eye size={20} color="#9C93AF" />
                        )}
                      </Button>
                    }
                  />
                  <Button
                    title="Sign In"
                    onPress={loginForm.handleSubmit(handleAuthQueryLogin)}
                    loading={isLoading}
                    className="mt-6"
                  />
                </View>
              </CardContent>
            </Card>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
