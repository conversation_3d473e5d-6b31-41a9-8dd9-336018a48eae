import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { Redirect } from 'expo-router';
import { useAuthStore } from '../src/stores/authStore';

export default function Index() {
  const { isAuthenticated, user, tokens, isLoading, initializeAuth } = useAuthStore();
  const [initStarted, setInitStarted] = useState(false);
  const [forceRedirect, setForceRedirect] = useState(false);

  console.log("user", user);
  console.log("tokens", tokens);
  console.log("isAuthenticated", isAuthenticated);
  console.log("isLoading", isLoading);
  console.log("forceRedirect", forceRedirect);

  // Initialize authentication on app start
  useEffect(() => {
    if (!initStarted) {
      setInitStarted(true);
      console.log("Starting auth initialization...");
      initializeAuth().catch((error) => {
        console.error("Auth initialization failed:", error);
        setForceRedirect(true);
      });
    }
  }, []); // Empty dependency array - only run once on mount

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoading && !forceRedirect) {
        console.warn('Authentication initialization taking too long, forcing redirect to auth');
        setForceRedirect(true);
      }
    }, 5000); // Reduced to 5 seconds

    return () => clearTimeout(timeout);
  }, [isLoading, forceRedirect]);

  // Show loading spinner while initializing (but not forever)
  if (isLoading && !forceRedirect) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  // Force redirect to auth if loading takes too long
  if (forceRedirect) {
    console.log("Force redirecting to auth due to timeout or error");
    return <Redirect href="/auth" />;
  }

  // Redirect based on authentication status
  if (isAuthenticated) {
    console.log("User is authenticated, redirecting to tabs");
    return <Redirect href="/(tabs)" />;
  }

  console.log("User is not authenticated, redirecting to auth");
  return <Redirect href="/auth" />;
}
