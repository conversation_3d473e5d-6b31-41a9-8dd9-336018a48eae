import React from 'react';
import { View, Text, ScrollView, RefreshControl, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { useShow, useShowHallContact, useShowSchedules, useShowPromoter } from '../../src/hooks/useApi';
import { Card, CardContent, CardHeader } from '../../src/components/ui/Card';
import { Button } from '../../src/components/ui/Button';
import { Stack } from 'expo-router';
import {
  ArrowLeft,
  Calendar,
  Clock,
  User,
  Phone,
  Mail,
  Building,
  FileText,
  Users,
  DollarSign,
  Eye
} from 'lucide-react-native';

export default function ShowDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const showId = parseInt(id || '0', 10);

  // Hide the default header
  React.useLayoutEffect(() => {
    // This will be handled by Stack.Screen in the layout
  }, []);

  const {
    data: show,
    isLoading: showLoading,
    error: showError,
    refetch: refetchShow
  } = useShow(showId);

  const {
    data: hallContact,
    isLoading: hallLoading,
    error: hallError,
    refetch: refetchHall
  } = useShowHallContact(showId);

  const {
    data: schedules,
    isLoading: schedulesLoading,
    error: schedulesError,
    refetch: refetchSchedules
  } = useShowSchedules(showId);

  const {
    data: promoter,
    isLoading: promoterLoading,
    error: promoterError,
    refetch: refetchPromoter
  } = useShowPromoter(showId);

  const isLoading = showLoading || hallLoading || schedulesLoading || promoterLoading;

  const handleRefresh = async () => {
    await Promise.all([
      refetchShow(),
      refetchHall(),
      refetchSchedules(),
      refetchPromoter(),
    ]);
  };

  const formatDate = (dateString: string | Date) => {
    try {
      const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Invalid Date';
    }
  };

  const formatTime = (timeString: string) => {
    try {
      // Handle both HH:MM:SS and HH:MM formats
      const timeParts = timeString.split(':');
      if (timeParts.length < 2) {
        return 'Invalid Time';
      }

      const date = new Date(`2000-01-01T${timeString}`);
      if (isNaN(date.getTime())) {
        return 'Invalid Time';
      }

      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });
    } catch (error) {
      console.error('Time formatting error:', error);
      return 'Invalid Time';
    }
  };

  // Debug logging
  React.useEffect(() => {
    if (showError) console.error('Show error:', showError);
    if (hallError) console.error('Hall error:', hallError);
    if (schedulesError) console.error('Schedules error:', schedulesError);
    if (promoterError) console.error('Promoter error:', promoterError);
  }, [showError, hallError, schedulesError, promoterError]);

  if (showLoading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-100">
        <View className="px-6 py-4">
          <TouchableOpacity
            onPress={() => router.back()}
            className="flex-row items-center mb-6"
          >
            <View className="bg-gray-50 p-2 rounded-full mr-3">
              <ArrowLeft size={20} color="#374151" />
            </View>
            <Text className="text-lg font-medium text-gray-900">Back</Text>
          </TouchableOpacity>

          <View className="gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="bg-white shadow-sm border-0" variant="elevated">
                <CardContent className="p-5">
                  <View className="animate-pulse">
                    <View className="h-6 bg-gray-200 rounded-lg w-3/4 mb-4" />
                    <View className="h-4 bg-gray-200 rounded w-1/2 mb-3" />
                    <View className="gap-3">
                      <View className="h-4 bg-gray-200 rounded w-full" />
                      <View className="h-4 bg-gray-200 rounded w-2/3" />
                      <View className="h-4 bg-gray-200 rounded w-3/4" />
                    </View>
                  </View>
                </CardContent>
              </Card>
            ))}
          </View>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state if there are critical errors
  if (showError && !show) {
    return (
      <SafeAreaView className="flex-1 bg-gray-100">
        <View className="px-6 py-4">
          <TouchableOpacity
            onPress={() => router.back()}
            className="flex-row items-center mb-6"
          >
            <View className="bg-gray-50 p-2 rounded-full mr-3">
              <ArrowLeft size={20} color="#374151" />
            </View>
            <Text className="text-lg font-medium text-gray-900">Back</Text>
          </TouchableOpacity>

          <Card className="bg-white shadow-sm border-0" variant="elevated">
            <CardContent className="p-6">
              <View className="items-center py-8">
                <View className="bg-red-100 p-4 rounded-full mb-4">
                  <FileText size={32} color="#dc2626" />
                </View>
                <Text className="text-xl font-bold text-gray-900 mb-2">
                  Error Loading Show
                </Text>
                <Text className="text-gray-600 text-center mb-6">
                  There was an error loading the show details. Please try again.
                </Text>
                <Button
                  title="Retry"
                  onPress={() => refetchShow()}
                  className="px-6 py-3"
                />
              </View>
            </CardContent>
          </Card>
        </View>
      </SafeAreaView>
    );
  }

  if (!show) {
    return (
      <SafeAreaView className="flex-1 bg-gray-100">
        <View className="px-6 py-4">
          <TouchableOpacity
            onPress={() => router.back()}
            className="flex-row items-center mb-6"
          >
            <View className="bg-gray-50 p-2 rounded-full mr-3">
              <ArrowLeft size={20} color="#374151" />
            </View>
            <Text className="text-lg font-medium text-gray-900">Back</Text>
          </TouchableOpacity>

          <Card className="bg-white shadow-sm border-0" variant="elevated">
            <CardContent className="p-6">
              <View className="items-center py-8">
                <View className="bg-gray-100 p-4 rounded-full mb-4">
                  <FileText size={32} color="#9ca3af" />
                </View>
                <Text className="text-xl font-bold text-gray-900 mb-2">
                  Show not found
                </Text>
                <Text className="text-gray-600 text-center mb-6">
                  The show you're looking for doesn't exist or has been removed.
                </Text>
                <Button
                  title="Go Back"
                  variant="outline"
                  onPress={() => router.back()}
                  className="px-6 py-3"
                />
              </View>
            </CardContent>
          </Card>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView className="flex-1 bg-gray-100">
        <ScrollView
          className="flex-1"
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={handleRefresh}
              tintColor="#005B63"
              colors={['#005B63']}
            />
          }
          showsVerticalScrollIndicator={false}
        >
        {/* Header */}
        <View className="px-6 py-8 bg-gradient-to-br from-main to-main/90">
          <TouchableOpacity
            onPress={() => router.back()}
            className="flex-row items-center mb-6"
          >
            <View className="bg-white/20 p-2 rounded-full mr-3">
              <ArrowLeft size={20} className='text-foreground' />
            </View>
            <Text className="text-lg font-medium text-foreground">Back to Shows</Text>
          </TouchableOpacity>

          <View className="flex-row items-start justify-between">
            <View className="flex-1 pr-4">
              <Text className="text-sm font-medium text-foreground uppercase tracking-wide mb-2">
                Show Details
              </Text>
              <Text className="text-2xl font-bold text-foreground mb-2">
                {show.name}
              </Text>
              <Text className="text-foreground font-mono bg-white/20 px-3 py-1 rounded-full self-start">
                {show.code}
              </Text>
            </View>
            <View className="flex-row gap-2">
      
              {!show.display && (
                <View className="bg-yellow-100 px-3 py-1 rounded-full flex-row items-center">
                  <Eye size={12} color="#d97706" />
                  <Text className="text-sm text-yellow-700 font-medium ml-1">Draft</Text>
                </View>
              )}
              {show.display  && (
                <View className="bg-green-100 px-3 py-1 rounded-full flex-row items-center">
                  <Eye size={12} color="#16a34a" />
                  <Text className="text-sm text-green-700 font-medium ml-1">Live</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        <View className="px-6 -mt-4 gap-6">
          {/* Basic Information */}
          <Card className="bg-white shadow-lg border-0" variant="elevated">
            <CardHeader className="pb-3">
              <Text className="text-xl font-bold text-gray-900">
                Show Information
              </Text>
            </CardHeader>
            <CardContent className="pt-0">
              <View className="gap-4">
                <View className="bg-gray-50 p-4 rounded-lg">
                  <View className="flex-row items-center mb-3">
                    <View className="bg-main/10 p-2 rounded-full mr-3">
                      <Calendar size={18} color="#005B63" />
                    </View>
                    <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      Event Duration
                    </Text>
                  </View>
                  <Text className="text-black font-medium text-base">
                    {formatDate(show.startDate)} - {formatDate(show.endDate)}
                  </Text>
                </View>

                <View className="bg-gray-50 p-4 rounded-lg">
                  <View className="flex-row items-center mb-3">
                    <View className="bg-orange-100 p-2 rounded-full mr-3">
                      <Clock size={18} color="#ea580c" />
                    </View>
                    <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                      Order Deadline
                    </Text>
                  </View>
                  <Text className="text-black font-medium text-base">
                    {formatDate(show.orderDeadlineDate)}
                  </Text>
                </View>

                {show.lateChargePercentage && (
                  <View className="bg-gray-50 p-4 rounded-lg">
                    <View className="flex-row items-center mb-3">
                      <View className="bg-green-100 p-2 rounded-full mr-3">
                        <DollarSign size={18} color="#16a34a" />
                      </View>
                      <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                        Late Charge
                      </Text>
                    </View>
                    <Text className="text-black font-medium text-base">
                      {show.lateChargePercentage}%
                    </Text>
                  </View>
                )}

                {show.description && (
                  <View className="bg-gray-50 p-4 rounded-lg">
                    <View className="flex-row items-center mb-3">
                      <View className="bg-blue-100 p-2 rounded-full mr-3">
                        <FileText size={18} color="#2563eb" />
                      </View>
                      <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                        Description
                      </Text>
                    </View>
                    <Text className="text-black font-medium text-base leading-6">
                      {show.description}
                    </Text>
                  </View>
                )}

                {show.link && (
                  <View className="bg-gray-50 p-4 rounded-lg">
                    <View className="flex-row items-center mb-3">
                      <View className="bg-purple-100 p-2 rounded-full mr-3">
                        <FileText size={18} color="#7c3aed" />
                      </View>
                      <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                        Link
                      </Text>
                    </View>
                    <Text className="text-blue-600 font-medium text-base">
                      {show.link}
                    </Text>
                  </View>
                )}
              </View>
            </CardContent>
          </Card>

          {/* Hall Contact Information */}
          {hallLoading ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Venue Information
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="animate-pulse gap-4">
                  <View className="h-4 bg-gray-200 rounded w-3/4" />
                  <View className="h-4 bg-gray-200 rounded w-1/2" />
                  <View className="h-4 bg-gray-200 rounded w-2/3" />
                </View>
              </CardContent>
            </Card>
          ) : hallError ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Venue Information
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="bg-red-50 p-4 rounded-lg">
                  <Text className="text-red-800 text-center">
                    Error loading venue information
                  </Text>
                </View>
              </CardContent>
            </Card>
          ) : hallContact ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Venue Information
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="gap-4">
                  <View className="bg-gray-50 p-4 rounded-lg">
                    <View className="flex-row items-center mb-3">
                      <View className="bg-blue-100 p-2 rounded-full mr-3">
                        <Building size={18} color="#2563eb" />
                      </View>
                      <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                        Hall
                      </Text>
                    </View>
                    <Text className="text-black font-medium text-base">
                      {hallContact.hallName} ({hallContact.hallCode})
                    </Text>
                  </View>

                  <View className="bg-gray-50 p-4 rounded-lg">
                    <View className="flex-row items-center mb-3">
                      <View className="bg-green-100 p-2 rounded-full mr-3">
                        <User size={18} color="#16a34a" />
                      </View>
                      <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                        Contact Person
                      </Text>
                    </View>
                    <Text className="text-black font-medium text-base">
                      {hallContact.contactName}
                    </Text>
                  </View>

                  <View className="flex-row gap-3">
                    <View className="flex-1 bg-gray-50 p-4 rounded-lg">
                      <View className="flex-row items-center mb-3">
                        <View className="bg-purple-100 p-1.5 rounded-full mr-2">
                          <Mail size={14} color="#7c3aed" />
                        </View>
                        <Text className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Email
                        </Text>
                      </View>
                      <Text className="text-blue-600 font-medium text-sm" numberOfLines={1}>
                        {hallContact.contactEmail}
                      </Text>
                    </View>

                    <View className="flex-1 bg-gray-50 p-4 rounded-lg">
                      <View className="flex-row items-center mb-3">
                        <View className="bg-orange-100 p-1.5 rounded-full mr-2">
                          <Phone size={14} color="#ea580c" />
                        </View>
                        <Text className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Phone
                        </Text>
                      </View>
                      <Text className="text-black font-medium text-sm" numberOfLines={1}>
                        {hallContact.contactPhone}
                      </Text>
                    </View>
                  </View>
                </View>
              </CardContent>
            </Card>
          ) : null}

          {/* Schedules */}
          {schedulesLoading ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Schedule
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="animate-pulse gap-4">
                  {[1, 2].map((i) => (
                    <View key={i} className="bg-gray-200 h-16 rounded-lg" />
                  ))}
                </View>
              </CardContent>
            </Card>
          ) : schedulesError ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Schedule
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="bg-red-50 p-4 rounded-lg">
                  <Text className="text-red-800 text-center">
                    Error loading schedule information
                  </Text>
                </View>
              </CardContent>
            </Card>
          ) : schedules && schedules.length > 0 ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Schedule
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="gap-4">
                  {schedules.map((schedule) => (
                    <View key={schedule.id} className="bg-gray-50 p-4 rounded-lg border-l-4 border-main">
                      <View className="flex-row items-center justify-between mb-3">
                        <Text className="font-bold text-black text-base">
                          {formatDate(schedule.showScheduleDate)}
                        </Text>
                        {schedule.showScheduleConfirmed && (
                          <View className="bg-green-100 px-3 py-1 rounded-full flex-row items-center">
                            <View className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                            <Text className="text-xs text-green-700 font-medium">Confirmed</Text>
                          </View>
                        )}
                      </View>

                      <View className="flex-row items-center mb-3">
                        <View className="bg-main/10 p-2 rounded-full mr-3">
                          <Clock size={16} color="#005B63" />
                        </View>
                        <Text className="text-black font-medium">
                          {formatTime(schedule.timeStart)} - {formatTime(schedule.timeEnd)}
                        </Text>
                      </View>

                      {schedule.showScheduleComments && (
                        <View className="bg-white p-3 rounded-lg mt-2">
                          <Text className="text-sm text-gray-600 leading-5">
                            {schedule.showScheduleComments}
                          </Text>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              </CardContent>
            </Card>
          ) : null}

          {/* Promoter Information */}
          {promoterLoading ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Promoter Information
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="animate-pulse gap-4">
                  <View className="h-4 bg-gray-200 rounded w-2/3" />
                  <View className="h-4 bg-gray-200 rounded w-1/2" />
                </View>
              </CardContent>
            </Card>
          ) : promoterError ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Promoter Information
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="bg-red-50 p-4 rounded-lg">
                  <Text className="text-red-800 text-center">
                    Error loading promoter information
                  </Text>
                </View>
              </CardContent>
            </Card>
          ) : promoter ? (
            <Card className="bg-white shadow-lg border-0" variant="elevated">
              <CardHeader className="pb-3">
                <Text className="text-xl font-bold text-gray-900">
                  Promoter Information
                </Text>
              </CardHeader>
              <CardContent className="pt-0">
                <View className="gap-4">
                  <View className="bg-gray-50 p-4 rounded-lg">
                    <View className="flex-row items-center mb-3">
                      <View className="bg-purple-100 p-2 rounded-full mr-3">
                        <Users size={18} color="#7c3aed" />
                      </View>
                      <Text className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                        Company
                      </Text>
                    </View>
                    <Text className="text-black font-medium text-base">
                      {promoter.companyName}
                    </Text>
                  </View>

                  {(promoter.floorPlanRequired || promoter.showSubcontact) && (
                    <View className="gap-3">
                      {promoter.floorPlanRequired && (
                        <View className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                          <View className="flex-row items-center">
                            <View className="bg-blue-100 p-2 rounded-full mr-3">
                              <FileText size={16} color="#2563eb" />
                            </View>
                            <Text className="text-blue-800 font-medium flex-1">
                              Floor plan required for this show
                            </Text>
                          </View>
                        </View>
                      )}

                      {promoter.showSubcontact && (
                        <View className="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                          <View className="flex-row items-center">
                            <View className="bg-yellow-100 p-2 rounded-full mr-3">
                              <Users size={16} color="#d97706" />
                            </View>
                            <Text className="text-yellow-800 font-medium flex-1">
                              Show subcontact enabled
                            </Text>
                          </View>
                        </View>
                      )}
                    </View>
                  )}
                </View>
              </CardContent>
            </Card>
          ) : null}
        </View>

        <View className="h-24" />
        </ScrollView>
      </SafeAreaView>
    </>
  );
}
