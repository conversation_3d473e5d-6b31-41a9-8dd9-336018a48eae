#!/bin/bash

# 🏠 Local Build Script for Mobile App
# Run this script to build APK and IPA locally

echo "🚀 Starting Local Build Process..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Run this script from the project root directory"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Generate native code if needed
echo "🔧 Generating native code..."
npx expo prebuild --clean

# Build Android APK
echo "🤖 Building Android APK..."
if command -v android &> /dev/null; then
    echo "✅ Android SDK found"
    npx expo run:android --variant release
    echo "📱 Android APK built successfully!"
    echo "📍 Location: android/app/build/outputs/apk/release/app-release.apk"
else
    echo "❌ Android SDK not found. Please install Android Studio and configure ANDROID_HOME"
fi

# Build iOS (macOS only)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Building iOS IPA..."
    if command -v xcodebuild &> /dev/null; then
        echo "✅ Xcode found"
        npx expo run:ios --configuration Release
        echo "📱 iOS build completed!"
    else
        echo "❌ Xcode not found. Please install Xcode from App Store"
    fi
else
    echo "⚠️  iOS builds require macOS with Xcode"
fi

echo "✅ Local build process completed!"
echo ""
echo "📁 Build outputs:"
echo "   Android: android/app/build/outputs/apk/release/"
echo "   iOS: ios/build/Build/Products/Release-iphoneos/"
