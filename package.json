{"name": "mobile_app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "reset-project": "node ./scripts/reset-project.js"}, "dependencies": {"@gluestack-ui/themed": "^1.1.73", "@hookform/resolvers": "^5.2.0", "@react-native-async-storage/async-storage": "^2.2.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "clsx": "^2.1.1", "expo": "~53.0.20", "expo-auth-session": "~6.2.1", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "lucide-react-native": "^0.532.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.61.1", "react-native": "0.79.5", "react-native-mmkv": "^2.12.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-web": "^0.20.0", "tailwindcss": "^3.4.17", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "sharp": "^0.34.3", "svg2png-many": "^0.0.7", "typescript": "~5.8.3"}, "private": true}