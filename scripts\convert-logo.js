const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function convertSvgToPng() {
  const svgPath = path.join(__dirname, '../assets/gss-logo.svg');
  const assetsDir = path.join(__dirname, '../assets');
  
  // Read SVG file
  const svgBuffer = fs.readFileSync(svgPath);
  
  try {
    // App icon (1024x1024 for high resolution)
    await sharp(svgBuffer)
      .resize(1024, 1024, { fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 0 } })
      .png()
      .toFile(path.join(assetsDir, 'icon.png'));
    console.log('✅ Created icon.png (1024x1024)');

    // Adaptive icon (1024x1024 for Android)
    await sharp(svgBuffer)
      .resize(1024, 1024, { fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 0 } })
      .png()
      .toFile(path.join(assetsDir, 'adaptive-icon.png'));
    console.log('✅ Created adaptive-icon.png (1024x1024)');

    // Splash screen icon (400x400)
    await sharp(svgBuffer)
      .resize(400, 400, { fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 0 } })
      .png()
      .toFile(path.join(assetsDir, 'splash-icon.png'));
    console.log('✅ Created splash-icon.png (400x400)');

    // Favicon (32x32)
    await sharp(svgBuffer)
      .resize(32, 32, { fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 0 } })
      .png()
      .toFile(path.join(assetsDir, 'favicon.png'));
    console.log('✅ Created favicon.png (32x32)');

    console.log('\n🎉 All logo files have been generated successfully!');
    console.log('📱 Your app now uses the GSS logo for:');
    console.log('   - App icon');
    console.log('   - Splash screen');
    console.log('   - Favicon');
    console.log('   - Android adaptive icon');
    
  } catch (error) {
    console.error('❌ Error converting SVG to PNG:', error);
  }
}

convertSvgToPng();
