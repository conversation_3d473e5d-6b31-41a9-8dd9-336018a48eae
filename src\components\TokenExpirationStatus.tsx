import React from 'react';
import { View, Text } from 'react-native';
import { useTokenExpirationInfo } from '../hooks/useTokenExpiration';
import { formatTimeUntilExpiration } from '../utils/tokenUtils';

/**
 * Component to display token expiration status (useful for debugging)
 */
export function TokenExpirationStatus() {
  const { tokens, timeUntilExpiration, isExpired, isExpiringSoon, hasTokens } = useTokenExpirationInfo();

  if (!hasTokens) {
    return (
      <View className="p-2 bg-gray-100 rounded-md">
        <Text className="text-sm text-gray-600">No authentication tokens</Text>
      </View>
    );
  }

  const timeRemaining = tokens ? formatTimeUntilExpiration(tokens) : 'Unknown';

  return (
    <View className={`p-2 rounded-md ${isExpired ? 'bg-red-100' : isExpiringSoon ? 'bg-yellow-100' : 'bg-green-100'}`}>
      <Text className={`text-sm font-medium ${isExpired ? 'text-red-800' : isExpiringSoon ? 'text-yellow-800' : 'text-green-800'}`}>
        Token Status: {isExpired ? 'Expired' : isExpiringSoon ? 'Expiring Soon' : 'Valid'}
      </Text>
      <Text className={`text-xs ${isExpired ? 'text-red-600' : isExpiringSoon ? 'text-yellow-600' : 'text-green-600'}`}>
        Time remaining: {timeRemaining}
      </Text>
      {timeUntilExpiration !== null && (
        <Text className="text-xs text-gray-500">
          Exact time: {Math.floor(timeUntilExpiration / 1000)}s
        </Text>
      )}
    </View>
  );
}
