import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { cn } from '../../utils/cn';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const buttonVariants = {
  primary: 'bg-main active:bg-main/90',
  secondary: 'bg-secondary active:bg-secondary/90',
  outline: 'border border-main bg-transparent active:bg-main/10',
  ghost: 'bg-transparent active:bg-muted',
};

const buttonSizes = {
  sm: 'px-3 py-2',
  md: 'px-4 py-3',
  lg: 'px-6 py-4',
};

const textVariants = {
  primary: 'text-main-foreground',
  secondary: 'text-secondary-foreground',
  outline: 'text-main',
  ghost: 'text-foreground',
};

const textSizes = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
};

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  className,
  children,
}: ButtonProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      className={cn(
        'rounded-lg items-center justify-center flex-row',
        buttonVariants[variant],
        buttonSizes[size],
        disabled && 'opacity-50',
        className
      )}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'outline' || variant === 'ghost' ? '#005B63' : '#ffffff'}
          className="mr-2"
        />
      )}
      {children || (
        <Text
          className={cn(
            'font-semibold',
            textVariants[variant],
            textSizes[size],
            disabled && 'opacity-70'
          )}
        >
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
}
