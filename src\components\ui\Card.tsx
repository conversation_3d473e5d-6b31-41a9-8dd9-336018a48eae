import React from 'react';
import { View, ViewProps } from 'react-native';
import { cn } from '../../utils/cn';

interface CardProps extends ViewProps {
  variant?: 'default' | 'elevated' | 'outlined';
  children: React.ReactNode;
}

const cardVariants = {
  default: 'bg-card',
  elevated: 'bg-card shadow-lg shadow-black/10',
  outlined: 'bg-card border border-border',
};

export function Card({ 
  variant = 'default', 
  className, 
  children, 
  ...props 
}: CardProps) {
  return (
    <View
      className={cn(
        'rounded-xl p-4',
        cardVariants[variant],
        className
      )}
      {...props}
    >
      {children}
    </View>
  );
}

export function CardHeader({ className, children, ...props }: ViewProps) {
  return (
    <View className={cn('mb-4', className)} {...props}>
      {children}
    </View>
  );
}

export function CardContent({ className, children, ...props }: ViewProps) {
  return (
    <View className={cn('', className)} {...props}>
      {children}
    </View>
  );
}

export function CardFooter({ className, children, ...props }: ViewProps) {
  return (
    <View className={cn('mt-4 flex-row justify-end', className)} {...props}>
      {children}
    </View>
  );
}
