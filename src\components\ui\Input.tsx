import React, { forwardRef } from 'react';
import { TextInput, View, Text, TextInputProps } from 'react-native';
import { cn } from '../../utils/cn';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerClassName?: string;
  inputClassName?: string;
}

export const Input = forwardRef<TextInput, InputProps>(
  (
    {
      label,
      error,
      helperText,
      leftIcon,
      rightIcon,
      containerClassName,
      inputClassName,
      className,
      ...props
    },
    ref
  ) => {
    return (
      <View className={cn('w-full', containerClassName)}>
        {label && (
          <Text className="text-sm font-medium text-foreground mb-2">
            {label}
          </Text>
        )}

        <View
          className={cn(
            'flex-row items-center border rounded-lg px-3 py-3',
            error
              ? 'border-destructive bg-destructive/10'
              : 'border-input bg-background focus:border-main',
            className
          )}
        >
          {leftIcon && <View className="mr-3">{leftIcon}</View>}

          <TextInput
            ref={ref}
            className={cn(
              'flex-1 text-base text-foreground',
              'android:py-0', // Remove default padding on Android
              inputClassName
            )}
            placeholderTextColor="#9C93AF"
            {...props}
          />

          {rightIcon && <View className="ml-3">{rightIcon}</View>}
        </View>

        {(error || helperText) && (
          <Text
            className={cn(
              'text-sm mt-1',
              error ? 'text-destructive' : 'text-muted-foreground'
            )}
          >
            {error || helperText}
          </Text>
        )}
      </View>
    );
  }
);
