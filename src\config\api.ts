import { Platform } from 'react-native';

// API Configuration for mobile app
export const API_CONFIG = {
  // Base URL for your API - must be configured in environment variables
  BASE_URL: process.env.API_BASE_URL,
  
  // Timeout settings
  TIMEOUT: 30000, // 30 seconds
  
  // Default headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // Retry configuration
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Platform-specific settings
  PLATFORM_SETTINGS: {
    web: {
      // Web-specific settings
      withCredentials: false,
    },
    native: {
      // Native-specific settings
      useSecureStore: true,
    },
  },
};

// Environment-specific configurations
export const getApiConfig = () => {
  const isDevelopment = __DEV__;
  const isWeb = Platform.OS === 'web';

  // Get the appropriate API base URL
  const baseUrl = isDevelopment
    ? (process.env.API_BASE_URL_DEV || process.env.API_BASE_URL)
    : process.env.API_BASE_URL;

  if (!baseUrl) {
    throw new Error(
      'API_BASE_URL environment variable is required. ' +
      'Please set API_BASE_URL in your .env file.'
    );
  }

  return {
    ...API_CONFIG,
    BASE_URL: baseUrl,

    // Platform-specific overrides
    ...API_CONFIG.PLATFORM_SETTINGS[isWeb ? 'web' : 'native'],
  };
};

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: 'auth/login',
    REGISTER: 'auth/register',
    REFRESH: 'auth/refresh',
    LOGOUT: 'auth/logout',
    PROFILE: 'auth/profile',
  },
  
  // User management
  USER: {
    PROFILE: 'user/profile',
    UPDATE: 'user/update',
    DELETE: 'user/delete',
  },
  
  // Add your specific endpoints here
  // Example:
  // POSTS: {
  //   LIST: 'posts',
  //   CREATE: 'posts',
  //   GET: (id: string) => `posts/${id}`,
  //   UPDATE: (id: string) => `posts/${id}`,
  //   DELETE: (id: string) => `posts/${id}`,
  // },
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your internet connection.',
  TIMEOUT_ERROR: 'Request timeout. Please try again.',
  UNAUTHORIZED: 'Session expired. Please log in again.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
};

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;
