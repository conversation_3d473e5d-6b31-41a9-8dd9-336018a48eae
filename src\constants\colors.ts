/**
 * Color constants for the mobile app
 * These colors match the Next.js project's design system
 */

export const Colors = {
  // Base colors
  background: '#ffffff',
  foreground: '#0a0a0a',
  
  // Card colors
  card: '#ffffff',
  cardForeground: '#0a0a0a',
  
  // Primary colors
  primary: '#005B63', // hsl(186 100% 19%)
  primaryForeground: '#fafafa',
  main: '#005B63', // Same as primary
  mainForeground: '#fafafa',
  
  // Secondary colors
  secondary: '#f5f5f5',
  secondaryForeground: '#171717',
  
  // Muted colors
  muted: '#f5f5f5',
  mutedForeground: '#737373',
  
  // Accent colors
  accent: '#f5f5f5',
  accentForeground: '#171717',
  
  // Status colors
  destructive: '#dc2626', // Red
  destructiveForeground: '#fafafa',
  success: '#16a34a', // Green
  successForeground: '#fafafa',
  warning: '#F59E0B', // Amber
  warningForeground: '#171717',
  
  // Gray colors
  gray: '#9C93AF',
  grayForeground: '#171717',
  
  // Border and input colors
  border: '#e5e5e5',
  input: '#e5e5e5',
  ring: '#0a0a0a',
  
  // Custom brand colors
  brand: {
    brown: '#74451C',
    brownForeground: '#fafafa',
    lime: '#D1DB00',
    limeForeground: '#171717',
    magenta: '#A40061',
    magentaForeground: '#fafafa',
  },
  
  // Chart colors
  chart: {
    1: '#f97316',
    2: '#06b6d4',
    3: '#8b5cf6',
    4: '#f59e0b',
    5: '#ef4444',
  },
};

// Dark mode colors
export const DarkColors = {
  // Base colors
  background: '#0a0a0a',
  foreground: '#fafafa',
  
  // Card colors
  card: '#0a0a0a',
  cardForeground: '#fafafa',
  
  // Primary colors
  primary: '#fafafa',
  primaryForeground: '#171717',
  main: '#005B63', // Keep the same main color
  mainForeground: '#fafafa',
  
  // Secondary colors
  secondary: '#262626',
  secondaryForeground: '#fafafa',
  
  // Muted colors
  muted: '#262626',
  mutedForeground: '#a3a3a3',
  
  // Accent colors
  accent: '#262626',
  accentForeground: '#fafafa',
  
  // Status colors
  destructive: '#dc2626', // Red
  destructiveForeground: '#fafafa',
  success: '#16a34a', // Green
  successForeground: '#fafafa',
  warning: '#F59E0B', // Amber
  warningForeground: '#171717',
  
  // Gray colors
  gray: '#9C93AF',
  grayForeground: '#171717',
  
  // Border and input colors
  border: '#262626',
  input: '#262626',
  ring: '#d4d4d8',
  
  // Custom brand colors (same as light mode)
  brand: {
    brown: '#74451C',
    brownForeground: '#fafafa',
    lime: '#D1DB00',
    limeForeground: '#171717',
    magenta: '#A40061',
    magentaForeground: '#fafafa',
  },
  
  // Chart colors
  chart: {
    1: '#3b82f6',
    2: '#10b981',
    3: '#f59e0b',
    4: '#8b5cf6',
    5: '#ef4444',
  },
};

// Helper function to get colors based on theme
export const getColors = (isDark: boolean = false) => {
  return isDark ? DarkColors : Colors;
};
