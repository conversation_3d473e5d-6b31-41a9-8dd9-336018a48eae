/**
 * Example usage of the mobile fetcher
 * This file demonstrates how to use the new mobile-optimized fetcher
 * that was adapted from your Next.js version
 *
 * IMPORTANT: Replace 'users', 'posts', etc. with your actual API endpoints
 */

import fetcher, { get, post, put, del, AppError } from '../services/fetcher';
import { apiService } from '../services/api';
import { API_ENDPOINTS } from '../config/api';

// Example 1: Basic GET request using the fetcher directly
// Replace 'users/${userId}' with your actual endpoint
export async function fetchUserProfile(userId: string) {
  try {
    const user = await get<User>(`users/${userId}`);
    return user;
  } catch (error) {
    if (error instanceof AppError) {
      console.error('User fetch failed:', error.message);
      // Handle user-safe errors differently
      if (error.isUserSafe) {
        // Show to user
        alert(error.message);
      } else {
        // Log for debugging, show generic message to user
        console.error('Internal error:', error);
        alert('Something went wrong. Please try again.');
      }
    }
    throw error;
  }
}

// Example 2: POST request with data
// Replace 'posts' with your actual endpoint
export async function createPost(postData: CreatePostData) {
  try {
    const newPost = await post<Post>('posts', postData);
    return newPost;
  } catch (error) {
    if (error instanceof AppError) {
      // Handle validation errors (usually 400-499)
      if (error.statusCode >= 400 && error.statusCode < 500) {
        throw new Error(error.message); // Re-throw with user-friendly message
      }
    }
    throw error;
  }
}

// Example 3: Using the apiService wrapper
export async function updateUserProfile(userId: string, updates: Partial<User>) {
  try {
    const updatedUser = await apiService.put<User>(
      API_ENDPOINTS.USER.UPDATE.replace(':id', userId),
      updates
    );
    return updatedUser;
  } catch (error) {
    console.error('Profile update failed:', error);
    throw error;
  }
}

// Example 4: File upload
export async function uploadAvatar(userId: string, imageFile: File) {
  try {
    const formData = new FormData();
    formData.append('avatar', imageFile);
    
    const result = await apiService.uploadFile<{ avatarUrl: string }>(
      `users/${userId}/avatar`,
      formData
    );
    
    return result.avatarUrl;
  } catch (error) {
    console.error('Avatar upload failed:', error);
    throw error;
  }
}

// Example 5: File download
export async function downloadReport(reportId: string) {
  try {
    const blob = await apiService.downloadFile(`reports/${reportId}/download`);
    
    // Create download link (web) or save file (mobile)
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `report-${reportId}.pdf`;
    link.click();
    
    // Clean up
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Report download failed:', error);
    throw error;
  }
}

// Example 6: Custom headers and options
export async function fetchWithCustomHeaders() {
  try {
    const data = await fetcher<any>('custom-endpoint', {
      method: 'GET',
      headers: {
        'X-Custom-Header': 'custom-value',
        'X-Client-Version': '1.0.0',
      },
    });
    return data;
  } catch (error) {
    console.error('Custom request failed:', error);
    throw error;
  }
}

// Example 7: Handling different response types
export async function fetchImageAsBlob(imageId: string) {
  try {
    // The fetcher automatically detects image content type and returns blob
    const imageBlob = await get<Blob>(`images/${imageId}`);
    
    // Convert to data URL for display
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(imageBlob);
    });
  } catch (error) {
    console.error('Image fetch failed:', error);
    throw error;
  }
}

// Example 8: Retry logic with exponential backoff
export async function fetchWithRetry<T>(
  url: string,
  options?: RequestInit,
  maxRetries: number = 3
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fetcher<T>(url, options);
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on client errors (4xx)
      if (error instanceof AppError && error.statusCode >= 400 && error.statusCode < 500) {
        throw error;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Exponential backoff
      const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

// Example 9: Batch requests
export async function fetchMultipleUsers(userIds: string[]) {
  try {
    const userPromises = userIds.map(id => get<User>(`users/${id}`));
    const users = await Promise.allSettled(userPromises);
    
    const successfulUsers = users
      .filter((result): result is PromiseFulfilledResult<User> => result.status === 'fulfilled')
      .map(result => result.value);
    
    const failedUsers = users
      .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
      .map(result => result.reason);
    
    if (failedUsers.length > 0) {
      console.warn('Some user fetches failed:', failedUsers);
    }
    
    return successfulUsers;
  } catch (error) {
    console.error('Batch user fetch failed:', error);
    throw error;
  }
}

// Example 10: Using with React Query (see useApi.ts for more examples)
export function useUserProfileQuery(userId: string) {
  // This would be used in a React component with React Query
  // See src/hooks/useApi.ts for actual implementation
  return {
    queryKey: ['user', userId],
    queryFn: () => fetchUserProfile(userId),
    enabled: !!userId,
  };
}

// Type definitions for examples
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

interface Post {
  id: string;
  title: string;
  content: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
}

interface CreatePostData {
  title: string;
  content: string;
}

// Migration notes from Next.js fetcher:
/*
Key differences from your Next.js version:

1. **No Server-Side Rendering**: Mobile apps don't have SSR, so no need for server/client detection
2. **Different Storage**: Uses SecureStore (native) or localStorage (web) instead of cookies
3. **No Proxy Routes**: Direct API calls instead of going through Next.js API routes
4. **Mobile-Specific Locale**: Uses Expo Localization instead of next-intl
5. **Platform Detection**: Uses React Native Platform instead of window detection
6. **Error Handling**: Enhanced for mobile with user-safe error flags
7. **Authentication**: Integrated with Zustand auth store instead of cookie-based auth
8. **File Handling**: Optimized for mobile file operations

Migration steps:
1. Replace cookie-based auth with SecureStore/localStorage
2. Update API base URLs to point directly to your backend
3. Replace next-intl locale detection with Expo Localization
4. Update error handling to use mobile-friendly alerts/toasts
5. Test on both web and native platforms
*/
