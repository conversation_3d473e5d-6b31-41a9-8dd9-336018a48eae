import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { apiService, endpoints, handleApiError, PaginatedResponse } from '../services/api';
import { User } from '../services/auth';
import { ShowInList } from '../models/Show';
import { ShowGeneralInfoData } from '../schemas/showSchema';
import ShowQuery from '../services/queries/ShowQuery';

// Query Keys
export const queryKeys = {
  users: ['users'] as const,
  user: (id: string) => ['users', id] as const,
  notifications: ['notifications'] as const,
  posts: ['posts'] as const,
  post: (id: string) => ['posts', id] as const,
  shows: ['shows'] as const,
  show: (id: number) => ['shows', id] as const,
  showHallContact: (id: number) => ['shows', id, 'hall-contact'] as const,
  showSchedules: (id: number) => ['shows', id, 'schedules'] as const,
  showPromoter: (id: number) => ['shows', id, 'promoter'] as const,
  showContacts: (id: number) => ['shows', id, 'contacts'] as const,
} as const;

// User Hooks
export function useUsers(options?: UseQueryOptions<User[]>) {
  return useQuery({
    queryKey: queryKeys.users,
    queryFn: () => apiService.get<User[]>(endpoints.users.list),
    ...options,
  });
}

export function useUser(id: string, options?: UseQueryOptions<User>) {
  return useQuery({
    queryKey: queryKeys.user(id),
    queryFn: () => apiService.get<User>(endpoints.users.detail(id)),
    enabled: !!id,
    ...options,
  });
}

export function useUpdateUser(options?: UseMutationOptions<User, Error, { id: string; data: Partial<User> }>) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => apiService.put<User>(endpoints.users.update(id), data),
    onSuccess: (data, variables) => {
      // Update the user in cache
      queryClient.setQueryData(queryKeys.user(variables.id), data);
      // Invalidate users list
      queryClient.invalidateQueries({ queryKey: queryKeys.users });
    },
    onError: handleApiError,
    ...options,
  });
}

// Notification Hooks
interface Notification {
  id: string;
  title: string;
  body: string;
  read: boolean;
  createdAt: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

export function useNotifications(options?: UseQueryOptions<PaginatedResponse<Notification>>) {
  return useQuery({
    queryKey: queryKeys.notifications,
    queryFn: () => apiService.get<PaginatedResponse<Notification>>(endpoints.notifications.list),
    ...options,
  });
}

export function useMarkNotificationAsRead(options?: UseMutationOptions<void, Error, string>) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => apiService.patch<void>(endpoints.notifications.markAsRead(id)),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.notifications });
    },
    onError: handleApiError,
    ...options,
  });
}

export function useMarkAllNotificationsAsRead(options?: UseMutationOptions<void, Error, void>) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => apiService.patch<void>(endpoints.notifications.markAllAsRead),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.notifications });
    },
    onError: handleApiError,
    ...options,
  });
}

// Post Hooks (Example resource)
interface Post {
  id: string;
  title: string;
  content: string;
  author: User;
  likes: number;
  liked: boolean;
  createdAt: string;
  updatedAt: string;
}

export function usePosts(options?: UseQueryOptions<PaginatedResponse<Post>>) {
  return useQuery({
    queryKey: queryKeys.posts,
    queryFn: () => apiService.get<PaginatedResponse<Post>>(endpoints.posts.list),
    ...options,
  });
}

export function usePost(id: string, options?: UseQueryOptions<Post>) {
  return useQuery({
    queryKey: queryKeys.post(id),
    queryFn: () => apiService.get<Post>(endpoints.posts.detail(id)),
    enabled: !!id,
    ...options,
  });
}

export function useCreatePost(options?: UseMutationOptions<Post, Error, Omit<Post, 'id' | 'author' | 'likes' | 'liked' | 'createdAt' | 'updatedAt'>>) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => apiService.post<Post>(endpoints.posts.create, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.posts });
    },
    onError: handleApiError,
    ...options,
  });
}

export function useUpdatePost(options?: UseMutationOptions<Post, Error, { id: string; data: Partial<Post> }>) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => apiService.put<Post>(endpoints.posts.update(id), data),
    onSuccess: (data, variables) => {
      queryClient.setQueryData(queryKeys.post(variables.id), data);
      queryClient.invalidateQueries({ queryKey: queryKeys.posts });
    },
    onError: handleApiError,
    ...options,
  });
}

export function useDeletePost(options?: UseMutationOptions<void, Error, string>) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => apiService.delete<void>(endpoints.posts.delete(id)),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: queryKeys.post(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.posts });
    },
    onError: handleApiError,
    ...options,
  });
}

export function useLikePost(options?: UseMutationOptions<void, Error, string>) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => apiService.post<void>(endpoints.posts.like(id)),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.post(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.posts });
    },
    onError: handleApiError,
    ...options,
  });
}

export function useUnlikePost(options?: UseMutationOptions<void, Error, string>) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiService.delete<void>(endpoints.posts.unlike(id)),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.post(id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.posts });
    },
    onError: handleApiError,
    ...options,
  });
}

// Show Hooks
export function useShows(options?: UseQueryOptions<ShowInList[]>) {
  return useQuery({
    queryKey: queryKeys.shows,
    queryFn: () => ShowQuery.getAll(),
    ...options,
  });
}

export function useShow(id: number, options?: UseQueryOptions<ShowGeneralInfoData>) {
  return useQuery({
    queryKey: queryKeys.show(id),
    queryFn: () => ShowQuery.getOne(id),
    enabled: !!id,
    ...options,
  });
}

export function useShowHallContact(id: number, options?: UseQueryOptions<any>) {
  return useQuery({
    queryKey: queryKeys.showHallContact(id),
    queryFn: () => ShowQuery.getHallContact(id),
    enabled: !!id,
    ...options,
  });
}

export function useShowSchedules(showId: number, options?: UseQueryOptions<any[]>) {
  return useQuery({
    queryKey: queryKeys.showSchedules(showId),
    queryFn: () => ShowQuery.getSchedules(showId),
    enabled: !!showId,
    ...options,
  });
}

export function useShowPromoter(showId: number, options?: UseQueryOptions<any>) {
  return useQuery({
    queryKey: queryKeys.showPromoter(showId),
    queryFn: () => ShowQuery.getPromoter(showId),
    enabled: !!showId,
    ...options,
  });
}

export function useShowContacts(showId: number, options?: UseQueryOptions<any>) {
  return useQuery({
    queryKey: queryKeys.showContacts(showId),
    queryFn: () => ShowQuery.getShowContacts(showId),
    enabled: !!showId,
    ...options,
  });
}
