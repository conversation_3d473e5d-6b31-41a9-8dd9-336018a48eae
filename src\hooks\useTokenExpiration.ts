import { useEffect, useRef } from 'react';
import { useAuthStore } from '../stores/authStore';
import { checkAndClearExpiredTokens, getTimeUntilExpiration } from '../utils/tokenUtils';

/**
 * Hook to automatically check for token expiration and clear expired tokens
 * @param checkInterval - How often to check for expiration in milliseconds (default: 60000 = 1 minute)
 */
export function useTokenExpiration(checkInterval: number = 60000) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const { tokens, isAuthenticated } = useAuthStore();

  useEffect(() => {
    // Only set up the interval if user is authenticated and has tokens
    if (isAuthenticated && tokens) {
      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // Set up periodic token expiration check
      intervalRef.current = setInterval(() => {
        const wasExpired = checkAndClearExpiredTokens();
        if (wasExpired) {
          console.log('Token expiration check: Tokens were expired and cleared');
        }
      }, checkInterval);

      // Also check immediately
      checkAndClearExpiredTokens();
    } else {
      // Clear interval if user is not authenticated
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAuthenticated, tokens, checkInterval]);
}

/**
 * Hook to get token expiration information
 * @returns Object with expiration info and helper functions
 */
export function useTokenExpirationInfo() {
  const { tokens } = useAuthStore();

  const timeUntilExpiration = tokens ? getTimeUntilExpiration(tokens) : null;
  const isExpired = timeUntilExpiration !== null && timeUntilExpiration <= 0;
  const isExpiringSoon = timeUntilExpiration !== null && timeUntilExpiration <= 5 * 60 * 1000; // 5 minutes

  return {
    tokens,
    timeUntilExpiration,
    isExpired,
    isExpiringSoon,
    hasTokens: !!tokens,
  };
}
