
// Contact model for list view (with string values for display)
export interface ContactInList {
  id: number;
  name: string;
  contactType: string;
  location: string;
  company: string;
  firstName: string;
  lastName: string;
  email: string;
  telephone: string;
  ext: string;
  cellphone: string;
  fax: string;
  isArchived: boolean;
  fullName: string;
}

// Contact model for detail view (with ID values for editing)
export interface Contact {
  id: number;
  name: string;
  contactTypeId: number;
  locationId?: number;
  companyId: number;
  firstName: string;
  lastName: string;
  email: string;
  telephone: string;
  ext: string;
  cellphone: string;
  isArchived: boolean;
  username: string;
  password: string;
}


export interface ContactDetail {
  contactId: number;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  telephone: string;
  ext: string;
  cellphone: string;
  contactTypeName: string;
  companyName: string;
  isArchived: boolean;
}
