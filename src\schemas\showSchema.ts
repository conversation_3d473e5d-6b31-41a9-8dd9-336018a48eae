import z from "zod";

const ShowGeneralInfoSchema = z
  .object({
    name: z.string().min(1, 'Show name is required'),
    code: z.string().min(1, 'Show code is required'),
    startDate: z.date(),
    endDate: z.date(),
    displayDate: z.date(),
    orderDeadlineDate: z.date(),
    lateChargePercentage: z.string(),
    link: z.string().optional(),
    description: z.string().optional(),
    display: z.boolean(),
    locationId: z.string().min(1, 'Location is required'),
    provinceId: z.string().min(1, 'Province is required'),
  })




export type ShowGeneralInfoData = z.infer<typeof ShowGeneralInfoSchema>;