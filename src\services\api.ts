import fetcher, { AppError } from './fetcher';
import { API_CONFIG } from '../config/api';

// API Configuration
const API_BASE_URL = process.env.API_BASE_URL || API_CONFIG.BASE_URL;

// API Response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Generic API methods using mobile fetcher
class ApiService {
  async get<T>(url: string, options?: RequestInit): Promise<T> {
    return fetcher<T>(url, { method: 'GET', ...options }, API_BASE_URL);
  }

  async post<T>(url: string, data?: any, options?: RequestInit): Promise<T> {
    return fetcher<T>(url, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    }, API_BASE_URL);
  }

  async put<T>(url: string, data?: any, options?: RequestInit): Promise<T> {
    return fetcher<T>(url, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    }, API_BASE_URL);
  }

  async patch<T>(url: string, data?: any, options?: RequestInit): Promise<T> {
    return fetcher<T>(url, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    }, API_BASE_URL);
  }

  async delete<T>(url: string, options?: RequestInit): Promise<T> {
    return fetcher<T>(url, { method: 'DELETE', ...options }, API_BASE_URL);
  }

  // Upload file
  async uploadFile<T>(url: string, formData: FormData, options?: RequestInit): Promise<T> {
    return fetcher<T>(url, {
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, let the browser set it
        ...options?.headers,
      },
      ...options,
    }, API_BASE_URL);
  }

  // Download file
  async downloadFile(url: string, options?: RequestInit): Promise<Blob> {
    return fetcher<Blob>(url, { method: 'GET', ...options }, API_BASE_URL);
  }
}

export const apiService = new ApiService();

// Specific API endpoints
export const endpoints = {
  // Auth endpoints
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    changePassword: '/auth/change-password',
    profile: '/auth/profile',
  },
  
  // User endpoints
  users: {
    list: '/users',
    detail: (id: string) => `/users/${id}`,
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`,
    avatar: (id: string) => `/users/${id}/avatar`,
  },
  
  // Notification endpoints
  notifications: {
    list: '/notifications',
    markAsRead: (id: string) => `/notifications/${id}/read`,
    markAllAsRead: '/notifications/read-all',
    settings: '/notifications/settings',
    registerDevice: '/notifications/devices',
  },
  
  // Example resource endpoints
  posts: {
    list: '/posts',
    detail: (id: string) => `/posts/${id}`,
    create: '/posts',
    update: (id: string) => `/posts/${id}`,
    delete: (id: string) => `/posts/${id}`,
    like: (id: string) => `/posts/${id}/like`,
    unlike: (id: string) => `/posts/${id}/unlike`,
  },
} as const;

// Re-export AppError from fetcher for compatibility
export { AppError } from './fetcher';

// Error handling utility for React Query and other consumers
export function handleApiError(error: any): void {
  if (error instanceof AppError) {
    // Error is already properly formatted
    console.error('API Error:', error.message, error.statusCode);
  } else {
    // Unexpected error type
    console.error('Unexpected error:', error);
  }
}
