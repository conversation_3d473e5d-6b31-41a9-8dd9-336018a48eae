import * as AuthSession from 'expo-auth-session';
import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';

const SECURE_STORE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
} as const;

// OAuth Configuration - Replace with your actual OAuth provider details
const OAUTH_CONFIG = {
  clientId: 'your-oauth-client-id',
  redirectUri: AuthSession.makeRedirectUri({
    scheme: 'mobile-app',
    path: 'auth',
  }),
  scopes: ['openid', 'profile', 'email'],
  additionalParameters: {},
  customParameters: {},
};

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: number;
}

class AuthService {
  private discovery = AuthSession.useAutoDiscovery('https://your-oauth-provider.com/.well-known/openid_configuration');

  // Secure storage methods
  async storeTokens(tokens: AuthTokens): Promise<void> {
    try {
      await SecureStore.setItemAsync(SECURE_STORE_KEYS.ACCESS_TOKEN, tokens.accessToken);
      if (tokens.refreshToken) {
        await SecureStore.setItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN, tokens.refreshToken);
      }
    } catch (error) {
      console.error('Error storing tokens:', error);
      throw new Error('Failed to store authentication tokens');
    }
  }

  async getStoredTokens(): Promise<AuthTokens | null> {
    try {
      const accessToken = await SecureStore.getItemAsync(SECURE_STORE_KEYS.ACCESS_TOKEN);
      const refreshToken = await SecureStore.getItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN);

      if (!accessToken) return null;

      return {
        accessToken,
        refreshToken: refreshToken || undefined,
      };
    } catch (error) {
      console.error('Error retrieving tokens:', error);
      return null;
    }
  }

  async storeUserData(user: User): Promise<void> {
    try {
      await SecureStore.setItemAsync(SECURE_STORE_KEYS.USER_DATA, JSON.stringify(user));
    } catch (error) {
      console.error('Error storing user data:', error);
      throw new Error('Failed to store user data');
    }
  }

  async getStoredUserData(): Promise<User | null> {
    try {
      const userData = await SecureStore.getItemAsync(SECURE_STORE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error retrieving user data:', error);
      return null;
    }
  }

  async clearStoredData(): Promise<void> {
    try {
      await Promise.all([
        SecureStore.deleteItemAsync(SECURE_STORE_KEYS.ACCESS_TOKEN),
        SecureStore.deleteItemAsync(SECURE_STORE_KEYS.REFRESH_TOKEN),
        SecureStore.deleteItemAsync(SECURE_STORE_KEYS.USER_DATA),
      ]);
    } catch (error) {
      console.error('Error clearing stored data:', error);
    }
  }

  // OAuth Authentication
  async signInWithOAuth(): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      const codeChallenge = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        Math.random().toString(36).substring(2, 15),
        { encoding: Crypto.CryptoEncoding.BASE64URL }
      );

      const request = new AuthSession.AuthRequest({
        clientId: OAUTH_CONFIG.clientId,
        scopes: OAUTH_CONFIG.scopes,
        redirectUri: OAUTH_CONFIG.redirectUri,
        responseType: AuthSession.ResponseType.Code,
        codeChallenge,
        codeChallengeMethod: AuthSession.CodeChallengeMethod.S256,
        additionalParameters: OAUTH_CONFIG.additionalParameters,
      });

      const result = await request.promptAsync(this.discovery);

      if (result.type === 'success') {
        const tokens = await this.exchangeCodeForTokens(result.params.code, codeChallenge);
        const user = await this.fetchUserProfile(tokens.accessToken);

        await this.storeTokens(tokens);
        await this.storeUserData(user);

        return { user, tokens };
      } else {
        throw new Error('Authentication was cancelled or failed');
      }
    } catch (error) {
      console.error('OAuth sign in error:', error);
      throw error;
    }
  }

  // Email/Password Authentication
  async signInWithEmailPassword(email: string, password: string): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // This would typically make a request to your backend's login endpoint
      // For now, returning mock data
      const tokens: AuthTokens = {
        accessToken: 'mock-login-access-token',
        refreshToken: 'mock-login-refresh-token',
        expiresAt: Date.now() + 3600000, // 1 hour from now
      };

      const user: User = {
        id: Math.random().toString(36).substring(2, 15),
        email,
        name: email.split('@')[0], // Use email prefix as name
        avatar: 'https://via.placeholder.com/150',
      };

      // Store the tokens and user data
      await this.storeTokens(tokens);
      await this.storeUserData(user);

      return { user, tokens };
    } catch (error) {
      console.error('Email/password sign in error:', error);
      throw error;
    }
  }

  async signUp(email: string, password: string, name?: string): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // This would typically make a request to your backend's signup endpoint
      // For now, returning mock data
      const tokens: AuthTokens = {
        accessToken: 'mock-signup-access-token',
        refreshToken: 'mock-signup-refresh-token',
        expiresAt: Date.now() + 3600000, // 1 hour from now
      };

      const user: User = {
        id: Math.random().toString(36).substring(2, 15),
        email,
        name: name || email.split('@')[0], // Use provided name or email prefix as fallback
        avatar: 'https://via.placeholder.com/150',
      };

      // Store the tokens and user data
      await this.storeTokens(tokens);
      await this.storeUserData(user);

      return { user, tokens };
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  private async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<AuthTokens> {
    // This would typically make a request to your OAuth provider's token endpoint
    // For now, returning mock data
    return {
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      expiresAt: Date.now() + 3600000, // 1 hour from now
    };
  }

  private async fetchUserProfile(accessToken: string): Promise<User> {
    // This would typically make a request to your OAuth provider's user info endpoint
    // For now, returning mock data
    return {
      id: '1',
      email: '<EMAIL>',
      name: 'John Doe',
      avatar: 'https://via.placeholder.com/150',
    };
  }

  async refreshTokens(refreshToken: string): Promise<AuthTokens> {
    try {
      // This would typically make a request to your OAuth provider's token refresh endpoint
      // For now, returning mock data
      const tokens: AuthTokens = {
        accessToken: 'new-mock-access-token',
        refreshToken: 'new-mock-refresh-token',
        expiresAt: Date.now() + 3600000,
      };

      await this.storeTokens(tokens);
      return tokens;
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  async signOut(): Promise<void> {
    try {
      await this.clearStoredData();
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  async isTokenValid(token: string): Promise<boolean> {
    try {
      // This would typically validate the token with your backend
      // For now, just checking if token exists
      return !!token;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }
}

export const authService = new AuthService();
