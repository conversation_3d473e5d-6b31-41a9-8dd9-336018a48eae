import axios from 'axios';
import { Platform } from 'react-native';
import { router } from 'expo-router';

import { AuthTokens } from './auth';
import { useAuthStore } from '../stores/authStore';
import { isTokenExpired } from '../utils/tokenUtils';

// Mobile-specific error class
export class AppError extends Error {
  public statusCode: number;
  public isUserSafe: boolean;

  constructor(message: string, statusCode: number, isUserSafe: boolean = false) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.isUserSafe = isUserSafe;
  }
}

// API Response interface
export interface ApiResponse<T> {
  data: T;
  message?: string;
  statusCode: number;
}

// Mobile storage adapter for tokens - reads from auth store
async function getStoredTokens(): Promise<AuthTokens | null> {
  try {
    // Get tokens from the auth store
    const authState = useAuthStore.getState();
    const tokens = authState.tokens;

    // Check if tokens exist and are not expired
    if (tokens && isTokenExpired(tokens)) {
      console.log('Access token is expired, clearing tokens');
      // Clear expired tokens
      await authState.signOut();
      return null;
    }

    return tokens;
  } catch (error) {
    console.error('Error getting stored tokens:', error);
    return null;
  }
}



// Get current locale for mobile
function getCurrentLocale(): string {
  try {
    // For web, try to get from navigator
    if (Platform.OS === 'web' && typeof navigator !== 'undefined') {
      const locale = navigator.language || navigator.languages?.[0] || 'fr';
      return locale.split('-')[0];
    }

    // For native, try to get from system locale
    // Note: You may want to install expo-localization for better locale detection
    // For now, we'll use a simple fallback
    return 'fr'; // Default fallback - you can customize this
  } catch (error) {
    console.error('Error getting locale:', error);
    return 'fr'; // Default fallback
  }
}

// Main fetcher function for mobile
export default async function fetcher<T>(
  url: string | URL | Request,
  init?: RequestInit | undefined,
  baseUrl?: string,
): Promise<T> {
  const locale = getCurrentLocale();
  const apiBaseUrl = baseUrl || process.env.EXPO_PUBLIC_API_URL;

  // Validate API base URL is configured
  if (!apiBaseUrl) {
    throw new AppError(
      'API_BASE_URL environment variable is not configured',
      0,
      false
    );
  }

  // Construct full URL
  const fullUrl = `${apiBaseUrl}/${url}`;
  console.log('Fetcher - Full URL:', fullUrl);

  // Get stored authentication tokens
  const auth = await getStoredTokens();
  console.log('Fetcher - Retrieved tokens:', auth ? { hasAccessToken: !!auth.accessToken, hasRefreshToken: !!auth.refreshToken } : 'null');

  // Prepare headers - fix type issue by being more explicit
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Accept-Language': locale,
  };

  // Add custom headers from init if provided
  if (init?.headers) {
    const customHeaders = init.headers as Record<string, string>;
    Object.assign(headers, customHeaders);
  }

  // Add authorization header if token exists
  if (auth?.accessToken) {
    headers.Authorization = `Bearer ${auth.accessToken}`;
    console.log('Fetcher - Added Authorization header for URL:', fullUrl);
  } else {
    console.log('Fetcher - No access token available for URL:', fullUrl);
  }
  
  try {
    const response = await fetch(fullUrl, {
      ...init,
      headers,
    });

    console.log("response",response)

    if (!response.ok) {
      // Handle 401 Unauthorized - token expired
      if (response.status === 401) {
        // Clear auth state and redirect to login
        const authStore = useAuthStore.getState();
        await authStore.signOut();

        // Redirect to login page
        router.replace('/auth');

        throw new AppError(
          'Session expired. Please log in again.',
          response.status,
          true
        );
      }

      // Parse error response
      let errorMessage = `HTTP error! Status: ${response.status}`;
      console.log('Fetcher - Error:', errorMessage);
      let isUserSafe = false;
      
      try {
        const errorData = await response.clone().json();
        if (errorData && errorData.message) {
          errorMessage = errorData.message;
          // 4xx errors are usually user-safe
          if (response.status >= 400 && response.status < 500) {
            isUserSafe = true;
          }
        }
      } catch (jsonError) {
        console.error('Failed to parse error response as JSON:', jsonError);
        try {
          const textError = await response.clone().text();
          errorMessage = `HTTP error! Status: ${response.status}. Response: ${textError.substring(0, 200)}${textError.length > 200 ? '...' : ''}`;
        } catch (textError) {
          console.error('Failed to read error response as text:', textError);
          errorMessage = `HTTP error! Status: ${response.status}. Failed to read response.`;
        }
        isUserSafe = false;
      }
      
      throw new AppError(errorMessage, response.status, isUserSafe);
    }

    // Handle different content types
    const contentType = response.headers.get('Content-Type') || '';
    
    // Handle binary content (files, images, etc.)
    if (
      contentType.includes('application/octet-stream') ||
      contentType.includes('application/pdf') ||
      contentType.includes('image/') ||
      contentType.includes('video/') ||
      contentType.includes('audio/')
    ) {
      return (await response.blob()) as T;
    }

    // Handle JSON response
    const data = (await response.json()) as ApiResponse<T>;

    // Check API-level status code
    if (data.statusCode !== 200 && data.statusCode !== 201) {
      const statusCode = data.statusCode ?? response.status;
      const isUserSafe = statusCode >= 400 && statusCode < 500;
      
      throw new AppError(
        data.message ?? 'Something went wrong',
        statusCode,
        isUserSafe
      );
    }

 

    return data.data;
  } catch (error) {
    console.error('Fetcher error for URL:', fullUrl, error);
    console.error('Error details:', {
      name: (error as any)?.name,
      message: (error as any)?.message,
      stack: (error as any)?.stack?.substring(0, 500),
      cause: (error as any)?.cause
    });

    // Re-throw AppError as-is
    if (error instanceof AppError) {
      throw error;
    }

    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new AppError(
        'Network error. Please check your internet connection or CORS settings.',
        0,
        true
      );
    }

    // Handle other errors
    throw new AppError(
      error instanceof Error ? error.message : 'An unexpected error occurred',
      500,
      false
    );
  }
}

// Axios instance for mobile (alternative to fetch)
const axiosInstance = axios.create({
  timeout: 30000, // 30 seconds timeout
});

// Request interceptor for axios
axiosInstance.interceptors.request.use(
  async (config) => {
    const locale = getCurrentLocale();
    const apiBaseUrl = process.env.EXPO_PUBLIC_API_URL;

    // Validate API base URL is configured
    if (!apiBaseUrl) {
      throw new Error('API_BASE_URL environment variable is not configured');
    }

    // Set base URL with locale
    config.baseURL = `${apiBaseUrl}/${locale}/api`;

    // Add authentication token
    const auth = await getStoredTokens();
    if (auth?.accessToken) {
      config.headers.Authorization = `Bearer ${auth.accessToken}`;
    }

    // Set locale header
    config.headers['Accept-Language'] = locale;

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for axios
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    // Handle 401 errors
    if (error.response?.status === 401) {
      const authStore = useAuthStore.getState();
      await authStore.signOut();

      // Redirect to login page
      router.replace('/auth');

      throw new AppError(
        'Session expired. Please log in again.',
        401,
        true
      );
    }
    
    // Convert axios error to AppError
    const statusCode = error.response?.status || 500;
    const message = error.response?.data?.message || error.message || 'An error occurred';
    const isUserSafe = statusCode >= 400 && statusCode < 500;
    
    throw new AppError(message, statusCode, isUserSafe);
  }
);

export { axiosInstance };

// Utility function for simple GET requests
export async function get<T>(url: string, baseUrl?: string): Promise<T> {
  return fetcher<T>(url, { method: 'GET' }, baseUrl);
}

// Utility function for POST requests
export async function post<T>(url: string, data?: any, baseUrl?: string): Promise<T> {
  return fetcher<T>(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  }, baseUrl);
}

// Utility function for PUT requests
export async function put<T>(url: string, data?: any, baseUrl?: string): Promise<T> {
  return fetcher<T>(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  }, baseUrl);
}

// Utility function for DELETE requests
export async function del<T>(url: string, baseUrl?: string): Promise<T> {
  return fetcher<T>(url, { method: 'DELETE' }, baseUrl);
}
