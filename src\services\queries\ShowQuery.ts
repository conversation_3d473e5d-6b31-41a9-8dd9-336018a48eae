import { AvailableTax } from '../../models/availableTax';
import { ContactDetail } from '../../models/Contact';
import { ShowInList, ShowPromoter, ShowSchedule } from '../../models/Show';
import { ShowGeneralInfoData } from '../../schemas/showSchema';
import fetcher from '../fetcher';

const ShowQuery = {
  tags: ['Shows'] as const,

  getAll: async () => fetcher<ShowInList[]>('Shows'),

  getOne: async (id: number) =>
    fetcher<ShowGeneralInfoData>(`Shows/${id}/general-info`),

  getHallContact: async (id: number) =>
    fetcher<{
      showId: number;
      hallId: number;
      contactId: number;
      hallName: string;
      hallCode: string;
      contactName: string;
      contactEmail: string;
      contactPhone: string;
    }>(`Shows/${id}/hall`),



  // --- Show Schedules ---

  getSchedules: async (showId: number) =>
    fetcher<ShowSchedule[]>(`Shows/${showId}/schedules`),
  getSchedule: async (id: number) =>
    fetcher<ShowSchedule>(`Shows/schedules/${id}`),





  // --- Show Promoter ---
  getPromoter: async (showId: number) =>
    fetcher<ShowPromoter>(`Shows/${showId}/promoter`),

  getAvailableTaxes: async (showId: number) =>
    fetcher<AvailableTax[]>(`Shows/${showId}/available-taxes`),



  getShowContacts: async (showId: number) =>
    fetcher<{
      showId: number;
      showName: string;
      showCode: string;
      companyName: string;
      billedToContacts: ContactDetail[];
      managerContacts: ContactDetail[];
    }>(`Shows/${showId}/contacts`),
};

export default ShowQuery;
