import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// Simple localStorage-based storage for all platforms
const storageAdapter = {
  getItem: (name: string) => {
    try {
      if (typeof localStorage !== 'undefined') {
        return localStorage.getItem(name);
      }
      return null;
    } catch (error) {
      console.warn('Storage getItem error:', error);
      return null;
    }
  },
  setItem: (name: string, value: string) => {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(name, value);
      }
    } catch (error) {
      console.warn('Storage setItem error:', error);
    }
  },
  removeItem: (name: string) => {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(name);
      }
    } catch (error) {
      console.warn('Storage removeItem error:', error);
    }
  },
};

interface AppState {
  // State
  theme: 'light' | 'dark' | 'system';
  language: string;
  isOnboardingCompleted: boolean;
  lastActiveTab: string;
  appVersion: string;
  isLoading: boolean;
  error: string | null;

  // Actions
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  setLanguage: (language: string) => void;
  completeOnboarding: () => void;
  setLastActiveTab: (tab: string) => void;
  setAppVersion: (version: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  resetApp: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      theme: 'system',
      language: 'en',
      isOnboardingCompleted: false,
      lastActiveTab: 'index',
      appVersion: '1.0.0',
      isLoading: false,
      error: null,

      // Actions
      setTheme: (theme) => {
        set({ theme });
      },

      setLanguage: (language) => {
        set({ language });
      },

      completeOnboarding: () => {
        set({ isOnboardingCompleted: true });
      },

      setLastActiveTab: (tab) => {
        set({ lastActiveTab: tab });
      },

      setAppVersion: (version) => {
        set({ appVersion: version });
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      setError: (error) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      resetApp: () => {
        set({
          theme: 'system',
          language: 'en',
          isOnboardingCompleted: false,
          lastActiveTab: 'index',
          isLoading: false,
          error: null,
        });
      },
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => storageAdapter),
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        isOnboardingCompleted: state.isOnboardingCompleted,
        lastActiveTab: state.lastActiveTab,
        appVersion: state.appVersion,
      }),
    }
  )
);
