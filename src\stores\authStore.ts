import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authService, User, AuthTokens } from '../services/auth';
import { isTokenExpired } from '../utils/tokenUtils';

// Cross-platform storage adapter
const storageAdapter = {
  getItem: async (name: string) => {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage for web
        if (typeof localStorage !== 'undefined') {
          return localStorage.getItem(name);
        }
        return null;
      } else {
        // Use AsyncStorage for native platforms
        return await AsyncStorage.getItem(name);
      }
    } catch (error) {
      console.warn('Storage getItem error:', error);
      return null;
    }
  },
  setItem: async (name: string, value: string) => {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage for web
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem(name, value);
        }
      } else {
        // Use AsyncStorage for native platforms
        await AsyncStorage.setItem(name, value);
      }
    } catch (error) {
      console.warn('Storage setItem error:', error);
    }
  },
  removeItem: async (name: string) => {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage for web
        if (typeof localStorage !== 'undefined') {
          localStorage.removeItem(name);
        }
      } else {
        // Use AsyncStorage for native platforms
        await AsyncStorage.removeItem(name);
      }
    } catch (error) {
      console.warn('Storage removeItem error:', error);
    }
  },
};

interface AuthState {
  // State
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  signIn: (email?: string, password?: string) => Promise<void>;
  signUp: (email: string, password: string, name?: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshTokens: () => Promise<void>;
  clearError: () => void;
  checkTokenExpiration: () => boolean;
  initializeAuth: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      signIn: async (email?: string, password?: string) => {
        set({ isLoading: true, error: null });
        try {
          let result;
          if (email && password) {
            // Email/password sign in
            result = await authService.signInWithEmailPassword(email, password);
          } else {
            // OAuth sign in
            result = await authService.signInWithOAuth();
          }

          const { user, tokens } = result;
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Authentication failed',
            isLoading: false,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      signUp: async (email: string, password: string, name?: string) => {
        set({ isLoading: true, error: null });
        try {
          const { user, tokens } = await authService.signUp(email, password, name);
          set({
            user,
            tokens,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Sign up failed',
            isLoading: false,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      signOut: async () => {
        set({ isLoading: true });
        try {
          await authService.signOut();
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Sign out failed',
            isLoading: false,
          });
        }
      },

      refreshTokens: async () => {
        const { tokens } = get();
        if (!tokens?.refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const newTokens = await authService.refreshTokens(tokens.refreshToken);
          set({ tokens: newTokens });
        } catch (error) {
          // If refresh fails, sign out the user
          get().signOut();
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Check if current tokens are expired and clear them if so
      checkTokenExpiration: () => {
        const { tokens } = get();
        if (tokens && isTokenExpired(tokens)) {
          console.log('Tokens are expired, clearing auth state');
          get().signOut();
          return true; // Tokens were expired and cleared
        }
        return false; // Tokens are still valid
      },

      initializeAuth: async () => {
        console.log('initializeAuth called');
        set({ isLoading: true });

        // Add a safety timeout to ensure loading state is cleared
        const safetyTimeout = setTimeout(() => {
          console.warn('Auth initialization safety timeout triggered');
          set({ isLoading: false });
        }, 8000);

        try {
          // Get current state from Zustand persistence
          const currentState = get();
          console.log('Auth initialization - current state:', {
            hasUser: !!currentState.user,
            hasTokens: !!currentState.tokens,
            isAuthenticated: currentState.isAuthenticated
          });

          // Check if we have stored tokens and user data
          if (currentState.tokens && currentState.user) {
            console.log('Found stored auth data, checking token expiration...');

            // Check if tokens are expired before making API call
            if (isTokenExpired(currentState.tokens)) {
              console.log('Stored tokens are expired, clearing auth state');
              set({
                user: null,
                tokens: null,
                isAuthenticated: false,
                isLoading: false,
                error: null,
              });
              return;
            }

            console.log('Tokens not expired, assuming user is authenticated');

            // Since tokens exist and are not expired, assume user is authenticated
            // We can validate in the background but don't block the UI
            set({
              isAuthenticated: true,
              isLoading: false,
            });

            // Optional: Validate token in background (don't await)
            (async () => {
              try {
                const AuthQuery = (await import('../services/queries/AuthQuery')).default;
                const userData = await AuthQuery.me();
                if (!userData) {
                  console.log('Background token validation failed, signing out');
                  set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                  });
                }
              } catch (error) {
                console.log('Background token validation error:', error);
                // Only sign out if it's a 401 error, otherwise keep user logged in
                if (error && typeof error === 'object' && 'status' in error && error.status === 401) {
                  set({
                    user: null,
                    tokens: null,
                    isAuthenticated: false,
                    error: null,
                  });
                }
              }
            })();
          } else {
            console.log('No stored auth data found - user needs to login');
            set({
              isLoading: false,
              isAuthenticated: false,
            });
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({
            error: error instanceof Error ? error.message : 'Initialization failed',
            isLoading: false,
            isAuthenticated: false,
          });
        } finally {
          clearTimeout(safetyTimeout);
          // Ensure loading is always set to false
          const currentState = get();
          if (currentState.isLoading) {
            console.log('Ensuring isLoading is set to false in finally block');
            set({ isLoading: false });
          }
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => storageAdapter),
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
