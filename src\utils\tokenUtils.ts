import { AuthTokens } from '../services/auth';
import { useAuthStore } from '../stores/authStore';

/**
 * Utility functions for token management and expiration checking
 */

// Helper function to check if token is expired
export function isTokenExpired(tokens: AuthTokens): boolean {
  if (!tokens.accessToken) return true;
  
  // If expiresAt is provided, check against it
  if (tokens.expiresAt) {
    const now = Date.now();
    const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
    return now >= (tokens.expiresAt - bufferTime);
  }
  
  // If no expiresAt, try to decode JWT to check expiration
  try {
    const payload = JSON.parse(atob(tokens.accessToken.split('.')[1]));
    if (payload.exp) {
      const now = Math.floor(Date.now() / 1000);
      const bufferTime = 5 * 60; // 5 minutes buffer in seconds
      return now >= (payload.exp - bufferTime);
    }
  } catch (error) {
    console.warn('Could not decode JWT token:', error);
    // If we can't decode the token, assume it might be expired
    return false;
  }
  
  return false;
}

/**
 * Check if current stored tokens are expired and clear them if so
 * @returns true if tokens were expired and cleared, false if tokens are still valid
 */
export function checkAndClearExpiredTokens(): boolean {
  const authStore = useAuthStore.getState();
  const tokens = authStore.tokens;
  
  if (tokens && isTokenExpired(tokens)) {
    console.log('Tokens are expired, clearing auth state');
    authStore.signOut();
    return true; // Tokens were expired and cleared
  }
  
  return false; // Tokens are still valid or don't exist
}

/**
 * Get the expiration time of the current access token
 * @returns Date object representing when the token expires, or null if no token or expiration info
 */
export function getTokenExpirationTime(tokens: AuthTokens): Date | null {
  if (!tokens.accessToken) return null;
  
  // If expiresAt is provided, use it
  if (tokens.expiresAt) {
    return new Date(tokens.expiresAt);
  }
  
  // Try to decode JWT to get expiration
  try {
    const payload = JSON.parse(atob(tokens.accessToken.split('.')[1]));
    if (payload.exp) {
      return new Date(payload.exp * 1000); // Convert from seconds to milliseconds
    }
  } catch (error) {
    console.warn('Could not decode JWT token:', error);
  }
  
  return null;
}

/**
 * Get time remaining until token expires
 * @returns number of milliseconds until expiration, or null if no expiration info
 */
export function getTimeUntilExpiration(tokens: AuthTokens): number | null {
  const expirationTime = getTokenExpirationTime(tokens);
  if (!expirationTime) return null;
  
  const now = Date.now();
  const timeRemaining = expirationTime.getTime() - now;
  
  return Math.max(0, timeRemaining); // Don't return negative values
}

/**
 * Format time remaining in a human-readable format
 * @returns string like "5 minutes" or "2 hours" or "expired"
 */
export function formatTimeUntilExpiration(tokens: AuthTokens): string {
  const timeRemaining = getTimeUntilExpiration(tokens);
  
  if (timeRemaining === null) return 'Unknown';
  if (timeRemaining <= 0) return 'Expired';
  
  const minutes = Math.floor(timeRemaining / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  
  return 'Less than a minute';
}
